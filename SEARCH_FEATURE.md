# Search Feature Implementation

## Overview
Added a new search functionality to the Telegram bot that allows users to search for models by name. The search feature includes:

1. **🔍 Search Button** - Added to the main menu
2. **Search Query Handler** - Processes user text input for search queries
3. **Database Search** - Searches both product names and model names
4. **Results Display** - Shows up to 10 matching products as inline buttons
5. **Product Integration** - Clicking search results opens the product page

## Files Modified

### 1. `src/common/mod.rs`
- Added `Search` and `SearchQuery` variants to the `Step` enum

### 2. `src/tiposhop/menu.rs`
- Added "🔍Поиск" button to the main menu
- Added search step handler to the menu branch

### 3. `src/db/mod.rs`
- Added `search_products()` method that searches both:
  - Product names (`products.name`)
  - Model names (`link_product_data.model_name`)
- Uses case-insensitive LIKE queries with wildcards
- Returns up to 10 results ordered by product name

### 4. `src/tiposhop/search.rs` (New File)
- `on_search()` - Displays search prompt to user
- `on_search_query()` - Processes user input and displays results
- Handles empty queries and no results scenarios
- Creates inline keyboard with search results

### 5. `src/tiposhop/mod.rs`
- Added `pub mod search;` to include the new search module

### 6. `src/main.rs`
- Added search branch to the main dispatcher tree

## User Flow

1. User clicks "🔍Поиск" button in main menu
2. Bot asks user to enter a model name
3. User types search query
4. Bot searches database and displays results:
   - If results found: Shows up to 10 products as inline buttons
   - If no results: Shows "nothing found" message
5. User clicks on a product button to view product details
6. User can start a new search or go back to menu

## Database Query

The search uses the following SQL query:

```sql
SELECT DISTINCT p.* FROM products p 
LEFT JOIN link_product_data lpd ON p.id = lpd.product_id 
WHERE p.enabled AND (
    p.name LIKE '%query%' COLLATE NOCASE OR 
    lpd.model_name LIKE '%query%' COLLATE NOCASE
)
ORDER BY p.name
LIMIT 10
```

## Features

- **Case-insensitive search** - Uses `COLLATE NOCASE`
- **Partial matching** - Uses `%query%` wildcards
- **Multiple field search** - Searches both product names and model names
- **Limited results** - Returns maximum 10 results to avoid overwhelming UI
- **Enabled products only** - Only searches enabled products
- **Duplicate removal** - Uses `DISTINCT` to avoid duplicate results
- **Sorted results** - Orders results by product name

## Error Handling

- Validates that user input is text (not media)
- Handles empty search queries
- Displays appropriate messages for no results
- Provides navigation options (new search, cart, back)

## Integration

- Seamlessly integrates with existing product page functionality
- Uses existing callback system for navigation
- Follows established UI patterns and Russian language interface
- Maintains consistent styling with menu video backgrounds
