[package]
name = "grammers-client"
version = "0.7.0"
authors = ["Lonami Exo <<EMAIL>>"]
license = "MIT OR Apache-2.0"
description = """
A high level client to interact with Telegram's API.
"""
homepage = "https://github.com/Lonami/grammers"
documentation = "https://docs.rs/grammers-client"
repository = "https://github.com/Lonami/grammers"
keywords = ["mtproto", "telegram", "tl"]
categories = ["api-bindings"]
edition = "2021"

[features]
markdown = ["pulldown-cmark"]
html = ["html5ever"]
proxy = ["grammers-mtsender/proxy"]
parse_invite_link = ["url"]

[dependencies]
chrono = "0.4.38"
futures-util = { version = "0.3.30", default-features = false, features = ["alloc"] }
grammers-crypto = { path = "../grammers-crypto", version = "0.7.0" }
grammers-mtproto = { path = "../grammers-mtproto", version = "0.7.0" }
grammers-mtsender = { path = "../grammers-mtsender", version = "0.7.0", features = ["metrics"] }
grammers-session = { path = "../grammers-session", version = "0.7.0" }
grammers-tl-types = { path = "../grammers-tl-types", version = "0.7.0", features = ["tl-mtproto"] }
html5ever = { version = "0.29.0", optional = true }
locate-locale = "0.2.0"
log = "0.4.22"
md5 = "0.7.0"
mime_guess = "2.0.5"
os_info = { version = "3.8.2", default-features = false }
pin-project-lite = "0.2"
metrics = "0.23"
pulldown-cmark = { version = "0.12.1", default-features = false, optional = true }
tokio = { version = "1.40.0", default-features = false, features = ["fs", "rt"] }
url = { version = "2.5.2", optional = true }

[dev-dependencies]
tokio = { version = "1.40.0", default-features = false, features = ["signal"] }
simple_logger = { version = "5.0.0", default-features = false, features = ["colors"] }
toml = "0.8.19"
