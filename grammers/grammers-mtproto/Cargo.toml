[package]
name = "grammers-mtproto"
version = "0.7.0"
authors = ["Lonami Exo <<EMAIL>>"]
license = "MIT OR Apache-2.0"
description = """
An implementation of the Mobile Transport Protocol.
"""
homepage = "https://github.com/Lonami/grammers"
documentation = "https://docs.rs/grammers-mtproto"
repository = "https://github.com/Lonami/grammers"
keywords = ["mtproto", "telegram", "tl"]
categories = ["network-programming"]
edition = "2021"

[dependencies]
bytes = "1.7.1"
crc32fast = "1.4.2"
flate2 = "1.0.33"
getrandom = "0.2.15"
grammers-crypto = { path = "../grammers-crypto", version = "0.7.0" }
grammers-tl-types = { path = "../grammers-tl-types", version = "0.7.0", features = ["tl-mtproto"] }
log = "0.4.22"
num-bigint = "0.4.6"
sha1 = "0.10.6"

[dev-dependencies]
toml = "0.8.19"
