[package]
name = "grammers-mtsender"
version = "0.7.0"
authors = ["Lonami Exo <<EMAIL>>"]
license = "MIT OR Apache-2.0"
description = """
A sender implementation using the Mobile Transport Protocol.
"""
homepage = "https://github.com/Lonami/grammers"
documentation = "https://docs.rs/grammers-mtsender"
repository = "https://github.com/Lonami/grammers"
keywords = ["api", "mtproto", "telegram", "tl"]
categories = ["api-bindings", "network-programming"]
edition = "2021"

[features]
proxy = ["tokio-socks", "trust-dns-resolver", "url"]
metrics = ["dep:metrics"]

[dependencies]
bytes = "1.7.1"
futures-util = { version = "0.3.30", default-features = false, features = ["alloc"] }
grammers-crypto = { path = "../grammers-crypto", version = "0.7.0" }
grammers-mtproto = { path = "../grammers-mtproto", version = "0.7.0" }
grammers-tl-types = { path = "../grammers-tl-types", version = "0.7.0", features = ["tl-mtproto"] }
log = "0.4.22"
tokio = { version = "1.40.0", default-features = false, features = ["net", "io-util", "sync", "time"] }
tokio-socks = { version = "0.5.2", optional = true }
trust-dns-resolver = { version = "0.23.2", optional = true }
url = { version = "2.5.2", optional = true }
metrics = { version = "0.23", optional = true }

[dev-dependencies]
simple_logger = { version = "5.0.0", default-features = false, features = ["colors"] }
tokio = { version = "1.40.0", features = ["rt"] }
toml = "0.8.19"
