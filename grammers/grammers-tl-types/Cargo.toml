[package]
name = "grammers-tl-types"
version = "0.7.0"
authors = ["Lonami Exo <<EMAIL>>"]
license = "MIT OR Apache-2.0"
description = """
Rust definitions for Telegram's API types and functions.
"""
homepage = "https://github.com/Lonami/grammers"
documentation = "https://docs.rs/grammers-tl-types"
repository = "https://github.com/Lonami/grammers"
keywords = ["telegram", "tl"]
categories = ["data-structures", "encoding"]
edition = "2021"
include = [
    "build.rs",
    "src/*.rs",
    "tl/*.tl",
]

[build-dependencies]
grammers-tl-gen = { path = "../grammers-tl-gen", version = "0.7.0" }
grammers-tl-parser = { path = "../grammers-tl-parser", version = "1.1.2" }

[dev-dependencies]
toml = "0.8.19"

[features]
default = ["impl-debug", "impl-from-enum", "impl-from-type", "tl-api"]

deserializable-functions = []
impl-debug = []
impl-from-enum = []
impl-from-type = []
tl-api = []
tl-mtproto = []
