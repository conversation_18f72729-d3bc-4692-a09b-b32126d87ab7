[package]
name = "grammers-tl-gen"
version = "0.7.0"
authors = ["Lonami Exo <<EMAIL>>"]
license = "MIT OR Apache-2.0"
description = """
Rust code generator from Telegram's API definitions.
"""
homepage = "https://github.com/Lonami/grammers"
documentation = "https://docs.rs/grammers-tl-gen"
repository = "https://github.com/Lonami/grammers"
keywords = ["telegram", "tl"]
categories = []
edition = "2021"

[dependencies]
grammers-tl-parser = { path = "../grammers-tl-parser", version = "1.1.2" }

[dev-dependencies]
toml = "0.8.19"
