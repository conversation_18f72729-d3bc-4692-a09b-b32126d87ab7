// Copyright 2020 - developers of the `grammers` project.
//
// Licensed under the Apache License, Version 2.0 <LICENSE-APACHE or
// https://www.apache.org/licenses/LICENSE-2.0> or the MIT license
// <LICENSE-MIT or https://opensource.org/licenses/MIT>, at your
// option. This file may not be copied, modified, or distributed
// except according to those terms.

//! This module contains all the different structures representing the
//! various terms of the [Type Language].
//!
//! [Type Language]: https://core.telegram.org/mtproto/TL
mod category;
mod definition;
mod flag;
mod parameter;
mod parameter_type;
mod ty;

pub use category::Category;
pub use definition::Definition;
pub use flag::Flag;
pub use parameter::Parameter;
pub use parameter_type::ParameterType;
pub use ty::Type;
