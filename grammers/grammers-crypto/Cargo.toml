[package]
name = "grammers-crypto"
version = "0.7.0"
authors = ["Lonami Exo <<EMAIL>>"]
license = "MIT OR Apache-2.0"
description = """
Several cryptographic utilities to work with Telegram's data.
"""
homepage = "https://github.com/Lonami/grammers"
documentation = "https://docs.rs/grammers-crypto"
repository = "https://github.com/Lonami/grammers"
keywords = ["telegram", "tl"]
categories = ["cryptography"]
edition = "2021"

[dependencies]
aes = "0.8.4"
getrandom = "0.2.15"
glass_pumpkin = "1.7.0"
hmac = "0.12.1"
num-bigint = "0.4.6"
pbkdf2 = "0.12.2"
sha1 = "0.10.6"
sha2 = "0.10.8"
num-traits = "0.2.19"

[dev-dependencies]
bencher = "0.1.5"
toml = "0.8.19"
