networks:
  app:
    name: app

services:
  db_porn:
    image: ghcr.io/tursodatabase/libsql-server:latest
    platform: linux/amd64
    ports:
      - "5001:5001"
      - "8080:8080"
    environment:
      - SQLD_NODE=primary
      - SQLD_HTTP_LISTEN_ADDR=0.0.0.0:8080
      - SQLD_DB_PATH=/var/lib/sqld/
      - SQLD_HTTP_AUTH=basic:dXNlcjpwYXNz
    volumes:
      - ./db_porn/:/var/lib/sqld/
    networks:
      - app

  server:
    build:
      dockerfile: Dockerfile
      context: .
    restart: unless-stopped
    environment:
      - TELOXIDE_TOKEN=${TELOXIDE_TOKEN}
      - CRYPTO_PAY_API_TOKEN=${CRYPTO_PAY_API_TOKEN}
      - REQUIRED_SUBSCRIPTION=${REQUIRED_SUBSCRIPTION}
      - NOTIFICATION_CHANNEL=${NOTIFICATION_CHANNEL}
      - DB_URL=http://db_porn:8080
    volumes:
      - ./assets:/app/assets
    networks:
      - app

  loki:
    image: grafana/loki:3.0.0
    ports:
      - "3100:3100"
    volumes:
      - ./configs/loki-config.yaml:/etc/loki/loki-config.yaml
    command: -config.file=/etc/loki/loki-config.yaml
    networks:
      - app

  grafana:
    image: grafana/grafana
    restart: unless-stopped
    ports:
      - 3000:3000
    environment:
      GF_SECURITY_DISABLE_INITIAL_ADMIN_CREATION: "true"
      GF_AUTH_ANONYMOUS_ENABLED: "true"
      GF_AUTH_ANONYMOUS_ORG_ROLE: "Admin"
      GF_AUTH_DISABLE_SIGNOUT_MENU: "true"
      GF_AUTH_DISABLE_LOGIN_FORM: "true"

      GF_FEATURE_TOGGLES_ENABLE: "accessControlOnCall lokiLogsDataplane"
      # GF_SERVER_ROOT_URL: "https://GRAFANA_EXTERNAL_HOST"
      GF_INSTALL_PLUGINS: "https://github.com/VictoriaMetrics/victoriametrics-datasource/releases/download/v0.8.2/victoriametrics-datasource-v0.8.2.zip;victoriametrics-datasource"
      GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS: "victoriametrics-datasource"
    volumes:
      - ./configs/grafana.yaml:/etc/grafana/provisioning/datasources/datasource.yaml
      - grafana_storage:/var/lib/grafana
    networks:
      - app

  logging:
    image: amir20/dozzle:latest
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./dozzle_data:/data
    ports:
      - "80:80"
    environment:
      - DOZZLE_LEVEL=debug
      - DOZZLE_ENABLE_ACTIONS=true
      - DOZZLE_NO_ANALYTICS=true
      - DOZZLE_AUTH_PROVIDER=simple
      - DOZZLE_BASE=/logs
    networks:
      - app

volumes:
  grafana_storage:
  mongo_data:
