use std::collections::HashSet;

use color_eyre::{eyre::Context as _, Report};
use teloxide::{
    payloads::{
        AnswerCallbackQuerySetters, EditMessageMediaSetters, SendMessageSetters, SendPhotoSetters,
        SendVideoSetters,
    },
    prelude::Requester,
    types::{
        ChatId, InlineKeyboardMarkup, InputMedia, InputMediaPhoto, InputMediaVideo, MessageId,
    },
    Bot,
};
use tracing::instrument;

use crate::{
    common::{ProductDescription, ProductId, Promocode, Step, User, UserId},
    db::Db,
    external::{crypto_pay, currency::CurrencyClient},
    media::Media,
    notifications::{Notification, NotificationClient},
};

#[derive(Clone)]
pub struct Context {
    pub db: Db,
    pub bot: Bot,
    pub bot_username: String,
    pub bot_message_id: Option<MessageId>,
    pub callback_query_id: Option<String>,
    pub user_message_id: Option<MessageId>,

    pub user: User,
    pub current_step: Step,

    pub notification_client: NotificationClient,
    pub crypto_pay: crypto_pay::CryptoPay,
    pub currency_client: CurrencyClient,
}

#[derive(Debug, Clone)]
pub enum MediaOrGroup {
    Media(Media),
    Group(Vec<Media>),
}

impl From<Media> for MediaOrGroup {
    fn from(value: Media) -> Self {
        Self::Media(value)
    }
}

impl From<Vec<Media>> for MediaOrGroup {
    fn from(value: Vec<Media>) -> Self {
        Self::Group(value)
    }
}

impl Context {
    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username))]
    pub async fn send(
        &self,
        text: impl Into<Option<String>>,
        media: impl Into<MediaOrGroup>,
        markup: InlineKeyboardMarkup,
    ) -> Result<MessageId, Report> {
        let text = text.into();
        let media = media.into();

        let msg = match media {
            MediaOrGroup::Media(media) => {
                // check if we already have sent this media with this bot
                let media_file_id = self
                    .get_media_file_id(&media)
                    .await
                    .wrap_err("Failed to get media id")?;
                let should_save = media_file_id.is_none();

                // grab input file, either by tg id, or load it from db
                let input_file = if let Some(media_file_id) = media_file_id {
                    tracing::trace!("Image file id: {:?}", media_file_id);
                    media_file_id
                } else {
                    tracing::trace!("Getting media from file");
                    self.get_media_from_file(&media)
                        .await
                        .wrap_err("Failed to get media from file")?
                };

                let msg = if let Some(message_id) = self.bot_message_id {
                    let input_media = if let Some(text) = text {
                        match media {
                            Media::Video(_) => InputMedia::Video(
                                InputMediaVideo::new(input_file)
                                    .caption(text)
                                    .parse_mode(teloxide::types::ParseMode::Html),
                            ),
                            Media::Photo(_) => InputMedia::Photo(
                                InputMediaPhoto::new(input_file)
                                    .caption(text)
                                    .parse_mode(teloxide::types::ParseMode::Html),
                            ),
                        }
                    } else {
                        match media {
                            Media::Video(_) => InputMedia::Video(InputMediaVideo::new(input_file)),
                            Media::Photo(_) => InputMedia::Photo(InputMediaPhoto::new(input_file)),
                        }
                    };

                    let resp = self
                        .bot
                        .edit_message_media(self.chat_id(), message_id, input_media)
                        .reply_markup(markup)
                        .await;

                    if let &Err(teloxide::RequestError::Api(
                        teloxide::ApiError::MessageNotModified,
                    )) = &resp
                    {
                        tracing::warn!("Message was not modified");

                        return Ok(message_id);
                    }

                    resp.wrap_err("Failed to edit message")?
                } else if let Some(text) = text {
                    match media {
                        Media::Video(_) => self
                            .bot
                            .send_video(self.chat_id(), input_file)
                            .caption(text)
                            .parse_mode(teloxide::types::ParseMode::Html)
                            .reply_markup(markup)
                            .await
                            .wrap_err("Failed to send message")?,
                        Media::Photo(_) => self
                            .bot
                            .send_photo(self.chat_id(), input_file)
                            .caption(text)
                            .parse_mode(teloxide::types::ParseMode::Html)
                            .reply_markup(markup)
                            .await
                            .wrap_err("Failed to send message")?,
                    }
                } else {
                    match media {
                        Media::Video(_) => self
                            .bot
                            .send_video(self.chat_id(), input_file)
                            .reply_markup(markup)
                            .await
                            .wrap_err("Failed to send message")?,
                        Media::Photo(_) => self
                            .bot
                            .send_photo(self.chat_id(), input_file)
                            .reply_markup(markup)
                            .await
                            .wrap_err("Failed to send message")?,
                    }
                };

                if should_save {
                    tracing::trace!("Should save to db");
                    self.save_media_id_from_message(&media, &msg)
                        .await
                        .wrap_err("Failed to save media id from message")?;
                }

                msg
            }
            MediaOrGroup::Group(group) => {
                let mut input_files = vec![];
                let mut should_save = false;
                let mut caption_set = false;

                for media in &group {
                    let media_file_id = self
                        .get_media_file_id(media)
                        .await
                        .wrap_err("Failed to get media id")?;
                    should_save = should_save || media_file_id.is_none();

                    // grab input file, either by tg id, or load it from db
                    let input_file = if let Some(media_file_id) = media_file_id {
                        tracing::trace!("Image file id: {:?}", media_file_id);
                        media_file_id
                    } else {
                        tracing::trace!("Getting media from file");
                        self.get_media_from_file(media)
                            .await
                            .wrap_err("Failed to get media from file")?
                    };

                    match media {
                        Media::Video(_) => {
                            if !caption_set {
                                input_files.push(InputMedia::Video(
                                    InputMediaVideo::new(input_file)
                                        .caption(text.clone().unwrap())
                                        .parse_mode(teloxide::types::ParseMode::Html),
                                ));
                                caption_set = true;
                            } else {
                                input_files
                                    .push(InputMedia::Video(InputMediaVideo::new(input_file)))
                            }
                        }
                        Media::Photo(_) => {
                            if !caption_set {
                                input_files.push(InputMedia::Photo(
                                    InputMediaPhoto::new(input_file)
                                        .caption(text.clone().unwrap())
                                        .parse_mode(teloxide::types::ParseMode::Html),
                                ));
                                caption_set = true;
                            } else {
                                input_files
                                    .push(InputMedia::Photo(InputMediaPhoto::new(input_file)))
                            }
                        }
                    };
                }

                let resp = self.bot.send_media_group(self.chat_id(), input_files).await;

                if should_save {
                    tracing::trace!("Should save to db");
                    let messages = resp?;
                    for (media, message) in group.iter().zip(messages) {
                        self.save_media_id_from_message(media, &message)
                            .await
                            .wrap_err("Failed to save media id from message")?;
                    }
                }

                self.bot
                    .send_message(self.chat_id(), "Выберите действие")
                    .reply_markup(markup)
                    .await
                    .wrap_err("Failed to send message")?
            }
        };

        Ok(msg.id)
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username))]
    pub async fn answer_cq(&self, text: &str) {
        let cq_id = self.callback_query_id.as_ref().expect("Should exist");

        if let Err(err) = self.bot.answer_callback_query(cq_id).text(text).await {
            tracing::error!("Failed to answer cq: {err}");
        }
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username, promo, referral))]
    pub fn notify_db_bought(
        &self,
        products: HashSet<ProductDescription>,
        promo: Option<Promocode>,
        referral: Option<UserId>,
    ) {
        if products.is_empty() {
            return;
        }

        let username_or_id = if let Some(username) = self.user.username.as_deref() {
            username.into()
        } else {
            self.user.id.into()
        };

        self.notification_client.send(Notification::DbBought {
            id: username_or_id,
            products,
            db: self.db.clone(),
            promo,
            referral,
        });
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username, referral))]
    pub async fn notify_user_referred(&self, referral: UserId) {
        let username_or_id = if let Some(username) = self.user.username.as_deref() {
            username.into()
        } else {
            self.user.id.into()
        };

        self.notification_client.send(Notification::UserReferred {
            id: username_or_id,
            referral,
        });
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username, promo))]
    pub fn notify_promocode_activated(&self, promo: Promocode) {
        let username_or_id = if let Some(username) = self.user.username.as_deref() {
            username.into()
        } else {
            self.user.id.into()
        };

        self.notification_client
            .send(Notification::PromocodeActivated {
                id: username_or_id,
                promo,
            });
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username))]
    pub async fn save_user(&self) -> Result<(), Report> {
        self.db.save_user(&self.user).await
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username))]
    pub async fn delete_bot_message(&mut self) -> Result<(), Report> {
        tracing::info!("Deleting bot message from ctx: {:?}", self.bot_message_id);
        let Some(message_id) = self.bot_message_id else {
            tracing::warn!("No bot message to delete");
            return Ok(());
        };
        self.bot
            .delete_message(ChatId(self.user.id.0), message_id)
            .await?;
        self.bot_message_id = None;

        Ok(())
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username))]
    pub async fn delete_user_message(&mut self) -> Result<(), Report> {
        tracing::info!("Deleting user message from ctx: {:?}", self.user_message_id);
        let Some(message_id) = self.user_message_id else {
            tracing::warn!("No user message to delete");
            return Ok(());
        };
        self.bot
            .delete_message(ChatId(self.user.id.0), message_id)
            .await?;
        self.user_message_id = None;

        Ok(())
    }

    pub fn chat_id(&self) -> ChatId {
        ChatId(self.user.id.0)
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username, product_id, promo))]
    pub async fn register_purchase(
        &self,
        product_id: ProductId,
        promo: &Option<Promocode>,
        paid: f64,
        production_cost: f64,
    ) -> Result<(), Report> {
        tracing::info!("Registering purchase: {product_id}");

        let ref_percent = if let Some(referral) = self.user.referred_by {
            Some(self.get_user_referral_percent(referral).await?)
        } else {
            None
        };

        self.db
            .register_purchase(
                self.user.id,
                product_id,
                promo.as_ref().map(|x| x.id.clone()),
                self.user.referred_by,
                ref_percent,
                paid,
                production_cost,
            )
            .await?;

        if let Some(percent) = ref_percent {
            if let Some(referral) = self.user.referred_by {
                self.db.add_balance(referral, paid * percent / 100.).await?;
            }
        }

        Ok(())
    }

    #[instrument(skip_all, fields(user_id = %self.user.id, bot = %self.bot_username))]
    pub async fn get_user_referral_percent(&self, user_id: UserId) -> Result<f64, Report> {
        let res = self.db.get_users_referred(user_id).await?;

        match res {
            x if x < 5 => Ok(0.),
            x if x < 15 => Ok(10.),
            x if x < 30 => Ok(15.),
            x if x < 50 => Ok(20.),
            _ => Ok(25.),
        }
    }
}
