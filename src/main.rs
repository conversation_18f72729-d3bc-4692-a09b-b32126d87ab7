mod common;
mod context;
mod db;
mod external;
mod media;
mod notifications;
mod telethon;
mod tiposhop;

use std::{
    str::FromStr,
    sync::{Arc, LazyLock},
};

use itertools::Itertools;
use reqwest::Url;
use teloxide::{
    dispatching::DpHandlerDescription,
    dptree::{
        self, case, entry, filter, filter_async, filter_map,
        prelude::{DependencyMap, Handler},
    },
    prelude::{ControlFlow, Dispatcher, Requester},
    types::{ChatId, InlineKeyboardButton, InlineKeyboardMarkup, Me, Message, Update, UpdateKind},
    Bot,
};

use color_eyre::Report;
use tracing::instrument;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::external::currency::CurrencyClient;
use crate::{
    common::*, context::Context, db::Db, external::crypto_pay::CryptoPay, media::Media,
    notifications::NotificationClient,
};

const ADMIN_URL: &str = "https://TIPOSHOP_ADMIN.t.me";
type HandlerResult<T = ()> = Result<T, Report>;
type Branch = Handler<'static, DependencyMap, HandlerResult, DpHandlerDescription>;

#[derive(Clone)]
struct ExternalClients {
    crypto_pay: CryptoPay,
    currency_client: CurrencyClient,
    notification_client: NotificationClient,
}

#[allow(clippy::needless_return)]
#[tokio::main]
async fn main() -> color_eyre::Result<()> {
    async_main().await
}

async fn async_main() -> color_eyre::Result<()> {
    dotenvy::dotenv().ok();
    // std::env::set_var("RUST_LOG", "ded_tg_shop=debug,teloxide=warn");
    // tracing_subscriber::fmt::init();

    // Setup logging
    {
        let (layer, task) = tracing_loki::builder()
            .label("service_name", "tg-shop")?
            .extra_field("run_id", uuid::Uuid::new_v4().to_string())?
            .build_url(Url::parse("http://loki:3100").unwrap())?;
        tokio::spawn(task);

        if std::env::var("TESTING").is_ok() {
            unsafe {
                std::env::set_var("RUST_LOG", "ded_tg_shop=debug,teloxide=warn");
            }
            let s = tracing_subscriber::registry()
                .with(tracing_subscriber::EnvFilter::from_default_env())
                .with(tracing_subscriber::fmt::layer());

            s.init();
        } else {
            unsafe {
                std::env::set_var("RUST_LOG", "ded_tg_shop=debug,teloxide=warn");
            }
            tracing_subscriber::registry()
                .with(tracing_subscriber::EnvFilter::from_default_env())
                .with(layer)
                .init();
        }
    }

    // Setup panic hook
    {
        color_eyre::config::HookBuilder::default()
            .panic_section("uncaught panic")
            .install()?;
    }

    tracing::info!("Starting dialogue bot...");

    let db_url = std::env::var("DB_URL").unwrap();

    let db = Db::connect_remote(db_url).await?;

    // Ensure assets directory exists
    media::ensure_assets_directory().await?;

    macro_rules! attr {
        ($name:literal) => {
            db.get_attribute($name)
                .await
                .ok()
                .flatten()
                .unwrap_or_else(|| std::env::var($name).unwrap())
                .parse()
                .unwrap()
        };
    }

    let crypto_pay_token = attr!("CRYPTO_PAY_API_TOKEN");
    let bot_token: String = attr!("TELOXIDE_TOKEN");
    let default_notification_channel = attr!("NOTIFICATION_CHANNEL");

    let crypto_pay = CryptoPay::new(crypto_pay_token);

    let notification_client =
        NotificationClient::new(default_notification_channel, db.clone(), bot_token.clone());
    notification_client.run_reports_loop();

    let bot = Bot::new(bot_token);

    let bot_me = bot.get_me().await.expect("Bot to work");
    let username = bot_me.username();
    tracing::info!("Starting bot as {username}");

    Dispatcher::builder(bot, tree())
        .dependencies(dptree::deps![
            Arc::new(db.clone()),
            db,
            ExternalClients {
                crypto_pay,
                currency_client: CurrencyClient::global().clone(),
                notification_client,
            }
        ])
        .enable_ctrlc_handler()
        .error_handler(Arc::new(|e: Report| async move {
            tracing::error!("Error: {e:?}");
        }))
        .build()
        .dispatch()
        .await;

    Ok(())
}

fn filter_cb() -> Branch {
    filter_map(|update: Update| match update.kind {
        UpdateKind::CallbackQuery(callback_query) => callback_query
            .data
            .and_then(|x| Callback::from_str(&x).ok()),
        _ => None,
    })
}

fn filter_step() -> Branch {
    filter_map(|update: Update| match update.kind {
        UpdateKind::CallbackQuery(callback_query) => callback_query
            .data
            .and_then(|x| Callback::from_str(&x).ok())
            .and_then(|x| match x {
                // Callback::Step(UserStep::Catalog(a)) => Some(a),
                // Callback::Step(UserStep::Admin(a)) => Some(a),
                Callback::Step(s) => Some(s),
                _ => None,
            }),
        _ => None,
    })
}

fn filter_message() -> Branch {
    filter_map(|update: Update| match update.kind {
        UpdateKind::Message(msg) => Some(msg),
        _ => None,
    })
}

fn filter_document() -> Branch {
    filter_map(|update: Update| match update.kind {
        UpdateKind::Message(msg) => msg.document().cloned(),
        _ => None,
    })
}

trait BranchExt {
    fn filter_message(self) -> Branch;
    fn filter_document(self) -> Branch;
    fn filter_step(self) -> Branch;
    fn filter_cb(self) -> Branch;
    fn filter_catalog_step(self) -> Branch;
    fn filter_admin_step(self) -> Branch;
}

impl BranchExt for Branch {
    #[track_caller]
    fn filter_message(self) -> Branch {
        self.chain(filter_message())
    }

    #[track_caller]
    fn filter_cb(self) -> Branch {
        self.chain(filter_cb())
    }

    #[track_caller]
    fn filter_document(self) -> Branch {
        self.chain(filter_document())
    }

    #[track_caller]
    fn filter_step(self) -> Branch {
        self.chain(filter_step())
    }

    #[track_caller]
    fn filter_catalog_step(self) -> Branch {
        self.chain(filter_map(|update: Update| match update.kind {
            UpdateKind::CallbackQuery(callback_query) => callback_query
                .data
                .and_then(|x| Callback::from_str(&x).ok())
                .and_then(|x| match x {
                    Callback::Step(Step::Catalog(a)) => Some(a),
                    _ => None,
                }),
            _ => None,
        }))
    }

    #[track_caller]
    fn filter_admin_step(self) -> Branch {
        self.chain(filter_map(|update: Update| match update.kind {
            UpdateKind::CallbackQuery(callback_query) => callback_query
                .data
                .and_then(|x| Callback::from_str(&x).ok())
                .and_then(|x| match x {
                    Callback::Step(Step::Admin(a)) => Some(a),
                    _ => None,
                }),
            _ => None,
        }))
    }
}

#[instrument(skip_all, fields(user_id = %upd.from().map(|x| x.id.0).unwrap_or(0), bot = %me.username()))]
async fn enter_ctx(upd: Update, db: Db, bot: Bot, me: Me, ext: ExternalClients) -> Option<Context> {
    let from = match &upd.kind {
        UpdateKind::ChatMember(m) => &m.new_chat_member.user,
        _ => upd.from()?,
    };

    let username = from.username.clone();

    let mut user = match db.get_user(UserId(from.id.0 as i64)).await {
        Ok(user) => user,
        Err(err) => {
            tracing::error!("Failed to get user: {:?}", err);
            return None;
        }
    };
    user.username = username;

    let bot_message_id = match &upd.kind {
        UpdateKind::CallbackQuery(callback_query) => callback_query.regular_message().map(|x| x.id), // this will shoot in the foot one day
        UpdateKind::Message(_) | UpdateKind::ChatMember(_) => None,
        _ => return None, // just go away if not a wanted interaction
    };

    let user_message_id = match &upd.kind {
        UpdateKind::Message(msg) => Some(msg.id),
        _ => None,
    };

    let (cq_id, callback) = match upd.kind {
        UpdateKind::CallbackQuery(callback_query) => (
            Some(callback_query.id),
            callback_query
                .data
                .and_then(|x| Callback::from_str(&x).ok()),
        ),
        _ => (None, None),
    };

    let current_step = user.path.back().cloned().unwrap_or(Step::Default);
    tracing::debug!("current_step: {:?}", current_step);
    if let Some(Callback::Step(step)) = &callback {
        tracing::debug!("Callback: {:?}", callback);

        let should_save_path = user.path.iter().last() != Some(step);
        if should_save_path {
            user.path.push_back(step.clone());
        }
    }

    let bot_username = me.username().to_owned();

    Some(Context {
        db,
        bot,
        bot_username,
        user,
        bot_message_id,
        callback_query_id: cq_id,
        user_message_id,
        current_step,
        notification_client: ext.notification_client,
        crypto_pay: ext.crypto_pay,
        currency_client: ext.currency_client,
    })
}

#[rustfmt::skip]
fn tree() -> Branch {
    entry()
        .filter_map_async(enter_ctx)

        .branch(filter(|upd: Update| {
            match upd.kind {
                UpdateKind::ChatMember(upd) => {
                    !upd.old_chat_member.is_present() && upd.new_chat_member.is_present()
                }
                _ => {
                    false
                }
            }
        }).endpoint(|ctx:Context| async move {
            tracing::info!("User subscribed to channel");
            tiposhop::menu::on_menu(ctx).await
        }))

        .branch(
            filter_async(|ctx: Context| async move {
                // static CHANNEL: LazyLock<ChatId> = LazyLock::new(|| ChatId(std::env::var("REQUIRED_SUBSCRIPTION").expect("req sub should present").parse().unwrap()));

                // let cm = ctx.bot
                //     .get_chat_member(
                //         *CHANNEL,
                //         teloxide::types::UserId(ctx.user.id.0 as i64),
                //     )
                //     .await;

                // match cm {
                //     Ok(cm) => {
                //         !cm.is_present()
                //     },
                //     Err(err) => {
                //         tracing::error!("Failed to check if user is a member of channel: {err}");
                //         true
                //     },
                // }

                false
            })
            .
            branch(filter_message().inspect_async(|mut ctx: Context, msg: Message| async move {
                let Some(text) = msg.text() else {
                    return;
                };

                let Some(suffix) = text.strip_prefix("/start ") else {
                    return;
                };

                match tiposhop::menu::try_handle_start_ext(&mut ctx, suffix).await {
                    Ok(_) => {},
                    Err(err) => {
                        tracing::error!("Failed to handle start ext: {err}");
                    },
                }
            }))
            .endpoint(|ctx: Context| async move {
                tracing::debug!("user is not a member of a channel");

                let resp = ctx
                    .send(
                        "🦍Для совершения покупок необходимо подписаться на новостной канал".to_owned(),
                        Media::video("icon"),
                        InlineKeyboardMarkup::new(vec![
                            row_btn_url("👉Подписаться", "https://t.me/+275RsKmcSpFkNWI9"),
                            row_btn("✅Проверить", Callback::Empty),
                        ])
                    )
                    .await;

                if let Err(err) = resp {
                    tracing::error!("Failed to ask user to join a channel: {err}");
                }

                Ok(())
            })
        )

        .branch(filter_cb()
            .branch(case![Callback::Back].endpoint(on_back))
            .branch(case![Callback::Restart].endpoint(tiposhop::menu::on_restart))
        )
        .branch(filter_step().branch(case![Step::Cart].endpoint(tiposhop::cart::on_cart)))

        .branch(filter_message().filter(|msg: Message| msg.text().map(|x| x.strip_prefix("/start").is_some()).unwrap_or(false)).endpoint(tiposhop::menu::on_start_message))

        .branch(tiposhop::menu::branch())
        .branch(tiposhop::admin::branch())
        .branch(tiposhop::catalog::branch())
        .branch(tiposhop::profile::branch())
        .branch(tiposhop::cart::branch())
        .branch(tiposhop::referral::branch())
        .branch(tiposhop::search::branch())

        .branch(filter_cb()
            .branch(case![Callback::Payed(id)].endpoint(tiposhop::profile::check_payment))
            .branch(case![Callback::Empty].endpoint(|ctx:Context| async move {
                ctx.bot.answer_callback_query(ctx.callback_query_id.unwrap()).await?;
                Ok(())
            }))
            .endpoint(|ctx: Context, callback: Callback| async move {
                tracing::error!("Unhandled callback {} at {}", callback, ctx.user.path.0.into_iter().map(|x| format!("{x:?}")).join(" -> "));
                Ok(())
            })
        )
        .branch(filter_message().endpoint(|ctx: Context, msg: Message| async move {
            if let Some(text) = msg.text() {
                tracing::error!("Unhandled text '{}' at {:?}", text, ctx.user.path);
            } else {
                tracing::error!("Unhandled msg: {msg:?} at {:?}", ctx.user.path);
            }
            Ok(())
        }))
        .endpoint(|upd: Update| async move {
            tracing::error!("Unhandled update: {:#?}", upd);
            Ok(())
        })
}

impl ProductDescription {
    pub fn button(&self, in_stock: bool) -> Vec<InlineKeyboardButton> {
        let additional = if in_stock {
            ""
        } else {
            " (нет в наличии)"
        };
        // if self.qty > 1 {
        //     row_btn(
        //         &format!("{} - {}${additional}", self.name, self.price),
        //         if in_stock {
        //             Callback::AddTraffic(self.id.clone())
        //         } else {
        //             Callback::Empty
        //         },
        //     )
        // } else {
        //     row_btn(
        //         &format!("{} - {}${additional}", self.name, self.price),
        //         if in_stock {
        //             Callback::Step(Step::Catalog(CatalogStep::Product(self.id.clone())))
        //         } else {
        //             Callback::Empty
        //         },
        //     )
        // }
        row_btn(
            &format!("DISABLED {}${additional}", &self.name),
            Callback::Empty,
        )
    }
}

fn row_btn(text: &str, callback: Callback) -> Vec<InlineKeyboardButton> {
    vec![InlineKeyboardButton::callback(text, callback.to_string())]
}

fn row_btn_if(text: &str, callback: Callback, cond: bool) -> Vec<InlineKeyboardButton> {
    if cond {
        row_btn(text, callback)
    } else {
        vec![]
    }
}

fn row_btn_cart() -> Vec<InlineKeyboardButton> {
    row_btn("🗑 Корзина", Callback::Step(Step::Cart))
}

fn row_btn_back() -> Vec<InlineKeyboardButton> {
    row_btn("Назад", Callback::Back)
}

fn row_btn_url(text: &str, url: &str) -> Vec<InlineKeyboardButton> {
    vec![InlineKeyboardButton::url(text, url.parse().unwrap())]
}

fn btn(text: &str, callback: Callback) -> InlineKeyboardButton {
    InlineKeyboardButton::callback(text, callback.to_string())
}

async fn trigger(update: Update, ctx: Context, me: Me) -> HandlerResult {
    let res = tree()
        .dispatch(dptree::deps![
            Arc::new(ctx.db.clone()),
            ctx.db,
            ExternalClients {
                crypto_pay: ctx.crypto_pay.clone(),
                currency_client: ctx.currency_client.clone(),
                notification_client: ctx.notification_client.clone(),
            },
            // teloxide adds by itself
            me,
            update,
            ctx.bot
        ])
        .await;

    match res {
        ControlFlow::Break(done) => done?,
        ControlFlow::Continue(f) => {
            panic!("Should not happen: {:?}", f);
        }
    }

    Ok(())
}

async fn on_back(mut ctx: Context, mut update: Update, me: Me) -> HandlerResult {
    tracing::debug!("on_back");

    let UpdateKind::CallbackQuery(cq) = &mut update.kind else {
        panic!("Should be callback query");
    };

    tracing::debug!("on_back: path: {:?}", ctx.user.path);
    if ctx.user.path.len() <= 1 {
        cq.data = Some(Callback::Step(Step::Menu).to_string());
    } else {
        let prev = {
            let _current_step = ctx.user.path.pop_back();
            tracing::debug!("on_back: current step: {:?}", _current_step);

            loop {
                let Some(step) = ctx.user.path.pop_back() else {
                    break None;
                };

                tracing::debug!("on_back: step: {:?}", step);

                if Some(&step) == _current_step.as_ref() {
                    tracing::debug!("on_back: ignored step: {:?}", step);
                    continue;
                }

                tracing::debug!("on_back: not ignored step: {:?}", step);
                break Some(step);
            }
        };

        cq.data = Some(Callback::Step(prev.unwrap()).to_string());
    }
    ctx.save_user().await?;

    trigger(update, ctx, me).await
}
