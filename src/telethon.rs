use std::{io::Write, net::Ipv4Addr};

use color_eyre::eyre::{eyre, Context, Report};
use grammers_session::Session;
use libsql::{params::IntoParams, Row};

async fn query_one(
    conn: &libsql::Connection,
    sql: &str,
    params: impl IntoParams,
) -> Result<Option<Row>, Report> {
    let mut stmt = conn.prepare(sql).await?;
    let mut rows = stmt.query(params).await?;
    let maybe_row = rows.next().await?;
    Ok(maybe_row)
}

pub async fn load_session_from_sqlite(data: &[u8]) -> Result<Session, Report> {
    let mut tf = tempfile::NamedTempFile::new()?;
    tf.write_all(data)?;

    let db = libsql::Builder::new_local(tf.path()).build().await?;
    let conn = db.connect()?;

    let version: Option<Row> = query_one(&conn, "SELECT * FROM version", ()).await?;

    match version {
        Some(row) => {
            let v: i64 = row.get(0)?;
            match v {
                7 => (),
                v => return Err(eyre!("Unsupported session version: {}", v)),
            }
        }
        None => {
            return Err(eyre!("No version"));
        }
    }

    let session_row = query_one(
        &conn,
        "SELECT dc_id, server_address, port, auth_key FROM sessions",
        (),
    )
    .await
    .wrap_err("Failed to execute query SELECT * FROM sessions")?;

    let Some(session_row) = session_row else {
        return Err(eyre!("No session"));
    };

    let dc_id: i32 = session_row.get(0)?;
    let server_address: String = session_row.get(1)?;
    let server_address: Ipv4Addr = server_address.parse().unwrap();
    let port: i32 = session_row.get(2)?;

    let addr = (server_address, port as u16).into();

    let auth = session_row.get::<[u8; 256]>(3)?;

    let session = Session::new();
    session.insert_dc(dc_id, addr, auth);
    Ok(session)
}
