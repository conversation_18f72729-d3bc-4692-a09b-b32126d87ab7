use color_eyre::{
    eyre::{eyre, Context as _},
    Report,
};
use serde::{Deserialize, Serialize};
use std::{fs, path::Path};
use teloxide::types::InputFile;
use tracing::instrument;

use crate::context::Context;

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub enum Media {
    Photo(String),
    Video(String),
}

impl<'de> Deserialize<'de> for Media {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        let (name, ext) = s.split_once('.').ok_or_else(|| {
            serde::de::Error::custom(format!(
                "Failed to deserialize media: {s}, should be name.ext"
            ))
        })?;
        match ext {
            "jpg" | "jpeg" | "png" => Ok(Media::Photo(name.to_owned())),
            "mp4" => Ok(Media::Video(name.to_owned())),
            _ => Err(serde::de::Error::custom(format!(
                "Failed to deserialize media: {s}, unknown ext {ext}"
            ))),
        }
    }
}

impl Serialize for Media {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        match self {
            Media::Photo(name) => serializer.serialize_str(&format!("{name}.jpg")),
            Media::Video(name) => serializer.serialize_str(&format!("{name}.mp4")),
        }
    }
}

impl Media {
    pub fn video(name: impl Into<String>) -> Self {
        Self::Video(name.into())
    }

    pub fn photo(name: impl Into<String>) -> Self {
        Self::Photo(name.into())
    }

    pub fn file_name(&self) -> String {
        match self {
            Media::Photo(name) => format!("{name}.jpg"),
            Media::Video(name) => format!("{name}.mp4"),
        }
    }

    pub fn name(&self) -> &str {
        match self {
            Media::Photo(name) => name,
            Media::Video(name) => name,
        }
    }

    // pub fn group(names: impl Into<Vec<String>>) -> Self {
    //     Self::Group(names.into())
    // }
}

// media files are loaded directly from filesystem

impl Context {
    #[instrument(skip_all, fields(media_name = %media.name(), bot = %self.bot_username))]
    pub async fn save_media_id_from_message(
        &self,
        media: &Media,
        message: &teloxide::types::Message,
    ) -> Result<(), Report> {
        tracing::debug!("Saving media ID from message for media: {}", media.name());

        // Extract file ID from the message based on media type
        let file_id = match media {
            Media::Photo(_) => {
                if let Some(photo_sizes) = message.photo() {
                    if let Some(largest_photo) = photo_sizes.last() {
                        Some(largest_photo.file.id.clone())
                    } else {
                        tracing::warn!("No photo sizes found in message");
                        None
                    }
                } else {
                    tracing::warn!("Expected photo but message contains no photo");
                    None
                }
            }
            Media::Video(_) => {
                if let Some(video) = message.video() {
                    Some(video.file.id.clone())
                } else {
                    tracing::warn!("Expected video but message contains no video");
                    None
                }
            }
        };

        if let Some(file_id) = file_id {
            tracing::info!(
                "Saving file ID '{}' for media '{}' with bot '{}'",
                file_id,
                media.name(),
                self.bot_username
            );

            self.db
                .set_file_id(&self.bot_username, media.name(), &file_id)
                .await
                .wrap_err("Failed to save media file ID to database")?;
        } else {
            tracing::error!(
                "Could not extract file ID from message for media: {}",
                media.name()
            );
        }

        Ok(())
    }

    #[instrument(skip_all, fields(media_name = %media.name()))]
    pub async fn get_media_from_file(&self, media: &Media) -> Result<InputFile, Report> {
        tracing::debug!("Loading media from file: {}", media.name());

        let assets_path = Path::new("assets");
        let file_path = assets_path.join(media.file_name());

        if !file_path.exists() {
            return Err(eyre!(
                "Media file '{}' not found at path: {:?}",
                media.name(),
                file_path
            ));
        }

        tracing::debug!("Found media file at: {:?}", file_path);
        Ok(InputFile::file(&file_path))
    }

    #[instrument(skip_all, fields(media_name = %media.name(), bot = %self.bot_username))]
    pub async fn get_media_file_id(&self, media: &Media) -> Result<Option<InputFile>, Report> {
        tracing::debug!("Getting cached file ID for media: {}", media.name());

        let file_id = self
            .db
            .get_file_id(&self.bot_username, media.name())
            .await
            .wrap_err("Failed to get media file ID from database")?;

        if let Some(file_id) = file_id {
            tracing::debug!(
                "Found cached file ID '{}' for media '{}' with bot '{}'",
                file_id,
                media.name(),
                self.bot_username
            );
            Ok(Some(InputFile::file_id(file_id)))
        } else {
            tracing::debug!(
                "No cached file ID found for media '{}' with bot '{}'",
                media.name(),
                self.bot_username
            );
            Ok(None)
        }
    }
}

/// Check if assets directory exists and create it if it doesn't
#[instrument]
pub async fn ensure_assets_directory() -> Result<(), Report> {
    let assets_path = Path::new("assets");

    if !assets_path.exists() {
        tracing::info!("Creating assets directory");
        fs::create_dir_all(assets_path).wrap_err("Failed to create assets directory")?;
    } else {
        tracing::debug!("Assets directory already exists");
    }

    Ok(())
}

/// Save media to file system
#[instrument(skip(data))]
pub async fn save_media_to_file(media: &Media, data: &[u8]) -> Result<(), Report> {
    // Ensure assets directory exists
    let assets_path = Path::new("assets");
    if !assets_path.exists() {
        fs::create_dir_all(assets_path).wrap_err("Failed to create assets directory")?;
        tracing::info!("Created assets directory");
    }

    let file_path = assets_path.join(media.file_name());
    fs::write(&file_path, data)
        .wrap_err_with(|| format!("Failed to save media file {:?}", file_path))?;

    tracing::info!(
        "Saved media '{}' to file system at {:?} ({} bytes)",
        media.name(),
        file_path,
        data.len()
    );

    Ok(())
}

/// Check if media file exists in the assets directory
pub fn media_file_exists(media_name: &str, is_video: bool) -> bool {
    let assets_path = Path::new("assets");
    let extension = if is_video { "mp4" } else { "jpg" };
    let file_path = assets_path.join(format!("{}.{}", media_name, extension));
    file_path.exists()
}

/// Get all media files currently in the assets directory
pub fn get_available_media_files() -> Result<Vec<String>, Report> {
    let assets_path = Path::new("assets");

    if !assets_path.exists() {
        return Ok(Vec::new());
    }

    let entries = fs::read_dir(assets_path).wrap_err("Failed to read assets directory")?;
    let mut media_names = Vec::new();

    for entry in entries {
        let entry = entry.wrap_err("Failed to read directory entry")?;
        let path = entry.path();

        if !path.is_file() {
            continue;
        }

        let Some(file_name) = path.file_name().and_then(|n| n.to_str()) else {
            continue;
        };

        // Extract media name from supported file types
        if let Some(name) = file_name
            .strip_suffix(".mp4")
            .or_else(|| file_name.strip_suffix(".jpg"))
            .or_else(|| file_name.strip_suffix(".jpeg"))
            .or_else(|| file_name.strip_suffix(".png"))
        {
            media_names.push(name.to_string());
        }
    }

    Ok(media_names)
}
