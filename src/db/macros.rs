#[macro_export]
macro_rules! db_enum {
    (pub enum $name:ident{
        $($val:ident),* $(,)?
    }) => {
        paste::paste!{
            #[derive(<PERSON>lone, Debug, PartialEq, Eq, ::serde::Serialize, ::serde::Deserialize, Co<PERSON>, Hash)]
            #[serde(rename_all = "snake_case")]
            pub enum $name {
                $($val),*
            }

            impl $crate::db::FromValue for $name {
                fn from_sql(value: libsql::Value) -> libsql::Result<Self>
                where
                    Self: Sized,
                {
                        match value {
                            libsql::Value::Text(text) => {
                                match text.as_str() {
                                    $(
                                        stringify!([<$val:snake>]) => Ok(Self::$val),
                                    )*
                                    s => Err(libsql::Error::Misuse(format!("Unrecognized {}: {s}", stringify!($name)))),
                                }
                            }
                            _ => Err(libsql::Error::Misuse(format!("{} should be TEXT", stringify!($name)))),
                        }

                }
            }

            impl From<$name> for libsql::Value {
                fn from(val: $name) -> libsql::Value {
                    libsql::Value::Text(
                        match val {
                            $(
                                $name::$val => stringify!([<$val:snake>]),
                            )*
                        }
                        .to_owned(),
                    )
                }
            }

            impl $name {
                $(
                    #[allow(dead_code)]
                    pub fn [<$val:snake>]() -> Self {
                        Self::$val
                    }

                    #[allow(dead_code)]
                    pub fn [<is_ $val:snake>](&self) -> bool {
                        matches!(self, Self::$val)
                    }
                )*
            }
        }
    };
}

#[macro_export]
macro_rules! id {
    ($id:ident($ty:ty) $(,$trait:ty)*) => {
        #[derive(Clone, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, $($trait),*)]
        pub struct $id(pub $ty);

        impl std::fmt::Display for $id {
            fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                self.0.fmt(f)
            }
        }

        impl std::str::FromStr for $id {
            type Err = <$ty as std::str::FromStr>::Err;

            fn from_str(s: &str) -> Result<Self, Self::Err> {
                Ok(Self(<$ty>::from_str(s)?))
            }
        }

        impl $crate::db::FromValue for $id {
            fn from_sql(value: libsql::Value) -> libsql::Result<Self>
            where
                Self: Sized,
            {
                Ok(Self(<$ty>::from_sql(value)?))
            }
        }

        impl From<$id> for libsql::Value {
            fn from(val: $id) -> libsql::Value {
                val.0.into()
            }
        }

        impl From<& $id> for libsql::Value {
            fn from(val: & $id) -> libsql::Value {
                val.to_owned().into()
            }
        }

        impl<'de> serde::Deserialize<'de> for $id {
            fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
            where
                D: serde::Deserializer<'de>,
            {
                Ok(Self(<$ty>::deserialize(deserializer)?))
            }
        }

        impl serde::Serialize for $id {
            fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
            where
                S: serde::Serializer,
            {
                self.0.serialize(serializer)
            }
        }

        impl $crate::common::NomParse for $id {
            fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
            {
                use nom::Parser;
                <$ty as $crate::common::NomParse>::parser().map(Self)
            }
        }
    };
}
id!(Test(i32));
