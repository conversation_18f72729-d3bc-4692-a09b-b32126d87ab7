use color_eyre::Report;
use libsql::{
    params::{IntoParams, IntoValue},
    Value,
};
use serde::{de::DeserializeOwned, Deserialize, Serialize};
use tracing::instrument;

use crate::db::DbInner;

pub trait FromValue {
    fn from_sql(value: Value) -> libsql::Result<Self>
    where
        Self: Sized;
}

impl FromValue for libsql::Value {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        Ok(val)
    }
}
impl FromValue for i32 {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Integer(i) => Ok(i as i32),
            _ => unreachable!("invalid value type"),
        }
    }
}
impl FromValue for u32 {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Integer(i) => Ok(i as u32),
            _ => unreachable!("invalid value type"),
        }
    }
}
impl FromValue for i64 {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Integer(i) => Ok(i),
            _ => unreachable!("invalid value type"),
        }
    }
}
impl FromValue for u64 {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Integer(i) => Ok(i as u64),
            _ => unreachable!("invalid value type"),
        }
    }
}
impl FromValue for f64 {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Real(f) => Ok(f),
            _ => unreachable!("invalid value type"),
        }
    }
}
impl FromValue for Vec<u8> {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Blob(blob) => Ok(blob),
            _ => unreachable!("invalid value type"),
        }
    }
}
impl<const N: usize> FromValue for [u8; N] {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Blob(blob) => blob
                .try_into()
                .map_err(|_| libsql::errors::Error::InvalidBlobSize(N)),
            _ => unreachable!("invalid value type"),
        }
    }
}
impl FromValue for String {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Text(s) => Ok(s),
            _ => unreachable!("invalid value type"),
        }
    }
}
impl FromValue for bool {
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Err(libsql::errors::Error::NullValue),
            Value::Integer(i) => match i {
                0 => Ok(false),
                1 => Ok(true),
                _ => Err(libsql::errors::Error::InvalidColumnType),
            },
            _ => unreachable!("invalid value type"),
        }
    }
}

impl<T> FromValue for Option<T>
where
    T: FromValue,
{
    fn from_sql(val: Value) -> libsql::Result<Self> {
        match val {
            Value::Null => Ok(None),
            _ => T::from_sql(val).map(Some),
        }
    }
}

#[derive(Clone, Debug, PartialEq)]
pub struct Json<T>(pub T);

impl<T> Json<T> {
    pub fn inner(self) -> T {
        self.0
    }
}

impl<T: Default> Default for Json<T> {
    fn default() -> Self {
        Self(Default::default())
    }
}

impl<'de, T: DeserializeOwned> Deserialize<'de> for Json<T> {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        match <Option<String>>::deserialize(deserializer)? {
            Some(s) => {
                let json = serde_json::from_str::<T>(&s)
                    .map_err(|x| serde::de::Error::custom(x.to_string()))?;
                Ok(Self(json))
            }
            None => {
                let json = serde_json::from_value::<T>(serde_json::Value::Null)
                    .map_err(|x| serde::de::Error::custom(x.to_string()))?;
                Ok(Self(json))
            }
        }
    }
}

impl<T: Serialize> Serialize for Json<T> {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        let s =
            serde_json::to_string(&self.0).map_err(|x| serde::ser::Error::custom(x.to_string()))?;
        s.serialize(serializer)
    }
}

impl<T: DeserializeOwned> FromValue for Json<T> {
    fn from_sql(value: Value) -> libsql::Result<Self>
    where
        Self: Sized,
    {
        match value {
            Value::Text(t) => serde_json::from_str(&t)
                .map_err(|err| libsql::Error::Misuse(format!("Failed to deserialize: {err}")))
                .map(Json),
            Value::Blob(b) => serde_json::from_slice(&b)
                .map_err(|err| libsql::Error::Misuse(format!("Failed to deserialize: {err}")))
                .map(Json),
            Value::Null => Err(libsql::errors::Error::NullValue),
            _ => unreachable!("invalid value type"),
        }
    }
}

impl<T: Serialize> IntoValue for Json<T> {
    fn into_value(self) -> libsql::Result<Value> {
        let s = serde_json::to_string(&self.0).unwrap();
        Ok(Value::Text(s))
    }
}

impl<T> std::ops::Deref for Json<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl<T> std::ops::DerefMut for Json<T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

impl<T> From<T> for Json<T> {
    fn from(value: T) -> Self {
        Self(value)
    }
}

impl DbInner {
    async fn conn(&self) -> libsql::Connection {
        if let Some(conn) = self.conn.get(&()).await {
            conn.clone()
        } else {
            let conn = self.db.connect().unwrap();
            self.conn.insert((), conn.clone()).await;
            conn
        }
    }

    #[instrument(skip_all, fields(%sql))]
    pub(super) async fn query_row<T: DeserializeOwned>(
        &self,
        sql: &str,
        params: impl IntoParams,
    ) -> Result<T, Report> {
        tracing::trace!("query row");

        let mut stmt = self.conn().await.prepare(sql).await?;
        let row = stmt.query_row(params).await?;

        let res = libsql::de::from_row(&row)?;
        Ok(res)
    }

    #[instrument(skip_all, fields(%sql))]
    pub(super) async fn query_one_col<T: FromValue>(
        &self,
        sql: &str,
        params: impl IntoParams,
    ) -> Result<T, Report> {
        tracing::trace!("query one col");

        let mut stmt = self.conn().await.prepare(sql).await?;
        let mut rows = stmt.query(params).await?;
        let row = rows.next().await?.unwrap();

        let res: Value = row.get(0)?;
        let res = T::from_sql(res)?;
        Ok(res)
    }

    #[instrument(skip_all, fields(%sql))]
    pub(super) async fn query_opt_row<T: DeserializeOwned>(
        &self,
        sql: &str,
        params: impl IntoParams,
    ) -> Result<Option<T>, Report> {
        let mut stmt = self.conn().await.prepare(sql).await?;
        let mut rows = stmt.query(params).await?;

        tracing::trace!("query opt");
        let row = rows.next().await?;

        match row {
            Some(row) => {
                let res = libsql::de::from_row(&row)?;
                Ok(Some(res))
            }
            None => Ok(None),
        }
    }

    #[instrument(skip_all, fields(%sql))]
    pub(super) async fn query_opt_col<T: FromValue>(
        &self,
        sql: &str,
        params: impl IntoParams,
    ) -> Result<Option<T>, Report> {
        tracing::trace!("query opt col");
        let mut stmt = self.conn().await.prepare(sql).await?;
        let mut rows = stmt.query(params).await?;
        let row = rows.next().await?;

        match row {
            Some(row) => {
                let res: Value = row.get(0)?;
                if !res.is_null() {
                    let res = T::from_sql(res)?;
                    Ok(Some(res))
                } else {
                    Ok(None)
                }
            }
            None => Ok(None),
        }
    }

    #[instrument(skip_all, fields(%sql))]
    pub(super) async fn query_col<T: FromValue>(
        &self,
        sql: &str,
        params: impl IntoParams,
    ) -> Result<Vec<T>, Report> {
        tracing::trace!("query");
        let mut stmt = self.conn().await.prepare(sql).await?;
        let mut rows = stmt.query(params).await?;
        let mut data = vec![];

        while let Some(row) = rows.next().await? {
            let res: libsql::Value = row.get(0)?;
            let res = T::from_sql(res)?;
            data.push(res)
        }

        Ok(data)
    }

    #[instrument(skip_all, fields(%sql))]
    pub(super) async fn query<T: DeserializeOwned>(
        &self,
        sql: &str,
        params: impl IntoParams,
    ) -> Result<Vec<T>, Report> {
        tracing::trace!("query");
        let mut stmt = self.conn().await.prepare(sql).await?;
        let mut rows = stmt.query(params).await?;
        let mut data = vec![];

        while let Some(row) = rows.next().await? {
            let res = libsql::de::from_row(&row)?;
            data.push(res)
        }

        Ok(data)
    }

    #[instrument(skip_all, fields(%sql))]
    pub(super) async fn execute(&self, sql: &str, params: impl IntoParams) -> Result<(), Report> {
        tracing::trace!("execute");
        let mut stmt = self.conn().await.prepare(sql).await?;
        stmt.execute(params).await?;

        Ok(())
    }
}
