use chrono::{DateTime, Utc};
use color_eyre::Report;
use once_cell::sync::Lazy;
use reqwest::Client as ReqwestClient;
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
// No unused imports

const URL: &str =
    "https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/rub.json";

// Example response:
// {
//     "date": "2025-04-16",
//     "rub": {
//         //...
//         "rub": 1,
//         "usd": 0.012004068,
//         "usdt": 0.012007498
//         // ...
//     }
// }

#[derive(Debug, Clone, Serialize, Deserialize)]
struct CurrencyResponse {
    date: String,
    rub: std::collections::HashMap<String, f64>,
}

#[derive(Debug, <PERSON><PERSON>)]
struct CurrencyCache {
    last_updated: DateTime<Utc>,
    rub_to_usd: f64,
    rub_to_usdt: f64,
}

impl Default for CurrencyCache {
    fn default() -> Self {
        Self {
            last_updated: Utc::now(),
            rub_to_usd: 0.012,  // Default fallback value
            rub_to_usdt: 0.012, // Default fallback value
        }
    }
}

static CURRENCY_CLIENT: Lazy<CurrencyClient> = Lazy::new(|| CurrencyClient {
    client: ReqwestClient::new(),
    cache: Arc::new(Mutex::new(CurrencyCache::default())),
});

#[derive(Debug, Clone)]
pub struct CurrencyClient {
    client: ReqwestClient,
    cache: Arc<Mutex<CurrencyCache>>,
}

impl CurrencyClient {
    pub fn global() -> &'static CurrencyClient {
        &CURRENCY_CLIENT
    }

    /// Get the RUB to USDT conversion rate
    pub async fn get_rub_to_usdt(&self) -> Result<f64, Report> {
        self.ensure_cache_fresh().await?;
        let cache = self.cache.lock().unwrap();
        Ok(cache.rub_to_usdt)
    }

    /// Convert RUB to USDT
    pub async fn rub_to_usdt(&self, amount: f64) -> Result<f64, Report> {
        let rate = self.get_rub_to_usdt().await?;
        Ok(amount * rate)
    }

    /// Ensure the cache is fresh (updated once per UTC day)
    async fn ensure_cache_fresh(&self) -> Result<(), Report> {
        let should_update = {
            let cache = self.cache.lock().unwrap();
            let now = Utc::now();
            let cache_date = cache.last_updated.date_naive();
            let today = now.date_naive();
            cache_date < today
        };

        if should_update {
            self.update_cache().await?
        }

        Ok(())
    }

    /// Update the currency cache
    async fn update_cache(&self) -> Result<(), Report> {
        tracing::debug!("Updating currency cache");

        let response = self
            .client
            .get(URL)
            .send()
            .await?
            .json::<CurrencyResponse>()
            .await?;

        let rub_to_usd = *response.rub.get("usd").unwrap_or(&0.012);
        let rub_to_usdt = *response.rub.get("usdt").unwrap_or(&0.012);

        let mut cache = self.cache.lock().unwrap();
        cache.last_updated = Utc::now();
        cache.rub_to_usd = rub_to_usd;
        cache.rub_to_usdt = rub_to_usdt;

        tracing::info!(
            "Updated currency rates: 1 RUB = {} USD, 1 RUB = {} USDT",
            rub_to_usd,
            rub_to_usdt
        );

        Ok(())
    }
}
