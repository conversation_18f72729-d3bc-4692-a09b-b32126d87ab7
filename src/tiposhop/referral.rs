use teloxide::{
    dptree::entry,
    types::{InlineKeyboardMarkup, Me},
};
use tracing::instrument;

use crate::{context::Context, media::Media, row_btn_back, Branch, HandlerResult};

#[rustfmt::skip]
pub fn branch() -> Branch {
    entry()
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_referral(ctx: Context, me: Me) -> HandlerResult {
    tracing::debug!("on_referral");

    let users_referred = ctx.db.get_users_referred(ctx.user.id).await?;
    let referral_income = ctx.db.get_user_referral_income(ctx.user.id).await?;
    let referral_percent = ctx.get_user_referral_percent(ctx.user.id).await?;

    let text = format!(
        "🦍Приглашайте активных пользователей в бот и зарабатывайте вместе с нами!\n\
        \n\
        Вы можете получить следующие вознаграждения за приглашённых пользователей:\n\
        \n\
        • <b>10%</b> - за <b>5</b> клиентов\n\
        • <b>15%</b> - за <b>15</b> клиентов\n\
        • <b>20%</b> - за <b>30</b> клиентов\n\
        • <b>25%</b> - за <b>50</b> клиентов\n\
        \n\
        Скопируйте вашу реферальную ссылку ниже, чтобы пригласить рефералов:\n\
        \n\
        • https://t.me/{}?start=ref-{}\n\
        \n\
        Статистика за всё время:\n\
        \n\
        Вы пригласили: <b>{}</b>\n\
        Ваш процент: <b>{}%</b>\n\
        Ваш заработок: <b>${:.2}</b>",
        me.username(),
        ctx.user.id,
        users_referred,
        referral_percent,
        referral_income,
    );

    ctx.send(
        Some(text),
        Media::video("referral"),
        InlineKeyboardMarkup::new(vec![row_btn_back()]),
    )
    .await?;
    ctx.save_user().await?;

    Ok(())
}
