use std::{str::FromStr, time::UNIX_EPOCH};

use chrono::Utc;

use itertools::Itertools;
use once_cell::sync::Lazy;
use teloxide::{
    dptree::{case, entry, filter_map},
    net::Download,
    payloads::CopyMessageSetters,
    prelude::Requester,
    types::{ChatId, InlineKeyboardMarkup, Me, Message, Update, UpdateKind},
};
use tokio::sync::Mutex;
use tracing::{instrument, Instrument};
use uuid::Uuid;

use crate::{
    common::{Callback, PromocodeKind, Step, UserId},
    context::Context,
    filter_message,
    media::Media,
    row_btn, row_btn_back, Branch, BranchExt, HandlerResult,
};

type MediaGroupEntry = (Media, Vec<u8>, Message);
type MediaGroupsMap = std::collections::HashMap<String, Vec<MediaGroupEntry>>;

#[derive(Debug, Clone)]
struct ModelData {
    category: String,
    name: String,
    price: f64,
    production_cost: f64,
    description: String,
}

type ModelDataMap = std::collections::HashMap<UserId, ModelData>;

static MEDIA_GROUPS: Lazy<Mutex<MediaGroupsMap>> =
    Lazy::new(|| Mutex::new(std::collections::HashMap::new()));

static MODEL_DATA: Lazy<Mutex<ModelDataMap>> =
    Lazy::new(|| Mutex::new(std::collections::HashMap::new()));

fn filter_admin_step() -> Branch {
    filter_map(|update: Update| match update.kind {
        UpdateKind::CallbackQuery(callback_query) => callback_query
            .data
            .and_then(|x| Callback::from_str(&x).ok())
            .and_then(|x| match x {
                Callback::Step(Step::Admin(a)) => Some(a),
                _ => None,
            }),
        _ => None,
    })
}

crate::string_enum!(
    pub enum AdminStep {
        Default,
        Mailing,
        Promocodes,
        CreatePromo,
        AddModel,
        AddModelSelectCategory(String),
        AddModelDetails,
    }
);

#[rustfmt::skip]
pub fn branch() -> Branch {
    entry()
        .map(|ctx: Context| ctx.current_step)
        .filter_map(|step: Step| match step {
            Step::Admin(a) => Some(a),
            _ => None,
        })
        .branch(case![AdminStep::Default]
            .branch(filter_admin_step()
                .branch(case![AdminStep::Mailing].endpoint(on_admin_mailing))
                .branch(case![AdminStep::Promocodes].endpoint(on_promocodes))
                .branch(case![AdminStep::AddModel].endpoint(on_add_model))
            )
        )
        .branch(case![AdminStep::AddModelSelectCategory(category)]
            .endpoint(on_add_model_select_category)
        )
        .branch(case![AdminStep::AddModelDetails]
            .branch(filter_message()
                .endpoint(on_add_model_details)
            )
        )
        .branch(case![AdminStep::AddModel]
            .branch(filter_message()
                .endpoint(on_add_model_media_group)
            )
        )
        .branch(case![AdminStep::Mailing]
            .branch(filter_message()
                .endpoint(on_admin_mailing_message)
            )
        )
        .branch(case![AdminStep::Promocodes].filter_admin_step()
            .branch(case![AdminStep::CreatePromo].endpoint(on_create_promo))
        )
        .branch(case![AdminStep::CreatePromo].filter_message().endpoint(on_create_promo_text))
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn cb(ctx: Context) -> HandlerResult {
    tracing::debug!("on_admin");

    let markup = vec![
        row_btn(
            "Добавить модель",
            Callback::Step(Step::Admin(AdminStep::AddModel)),
        ),
        row_btn(
            "Промокоды",
            Callback::Step(Step::Admin(AdminStep::Promocodes)),
        ),
        row_btn("Рассылка", Callback::Step(Step::Admin(AdminStep::Mailing))),
        row_btn("Назад", Callback::Back),
    ];

    let text = "Админка";

    ctx.send(
        Some(text.to_owned()),
        Media::video("menu"),
        InlineKeyboardMarkup::new(markup),
    )
    .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_add_model(ctx: Context) -> HandlerResult {
    let text = "Выбери категорию для новой модели:";

    // Get categories from database
    let categories = ctx.db.get_all_product_categories().await?;

    let markup = InlineKeyboardMarkup::new(
        categories
            .iter()
            .map(|category| {
                row_btn(
                    &category.id,
                    Callback::Step(Step::Admin(AdminStep::AddModelSelectCategory(
                        category.id.clone(),
                    ))),
                )
            })
            .chain(vec![row_btn_back()]),
    );

    ctx.send(Some(text.to_owned()), Media::video("menu"), markup)
        .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username, category = %category))]
pub async fn on_add_model_select_category(mut ctx: Context, category: String) -> HandlerResult {
    // Store the selected category and ask for model details
    let model_data = ModelData {
        category,
        name: "".to_owned(),
        price: 0.0,
        production_cost: 0.0,
        description: "".to_owned(),
    };

    {
        let mut data = MODEL_DATA.lock().await;
        data.insert(ctx.user.id, model_data);
    }

    let text = "Отлично! Теперь введи данные модели в следующем формате:\n\n\
        <b>Имя модели</b>\n\
        <b>Цена (в USD)</b>\n\
        <b>Себестоимость (в USD)</b>\n\n\
        Пример:\n\
        <blockquote>Анна Красивая\n\
        25.50\n\
        5.00</blockquote>";

    ctx.send(
        Some(text.to_owned()),
        Media::video("menu"),
        InlineKeyboardMarkup::new(vec![row_btn_back()]),
    )
    .await?;

    // Change step to handle model details input
    ctx.user
        .path
        .push_back(Step::Admin(AdminStep::AddModelDetails));
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_add_model_details(mut ctx: Context, message: Message) -> HandlerResult {
    let Some(text) = message.text() else {
        ctx.send(
            Some("Ожидал от тебя текст, попробуй еще раз".to_owned()),
            Media::video("menu"),
            InlineKeyboardMarkup::new(vec![row_btn_back()]),
        )
        .await?;
        return Ok(());
    };

    let lines: Vec<&str> = text.lines().collect();
    if lines.len() != 3 {
        ctx.send(
            Some("Ожидал 3 строки (имя, цена, себестоимость), попробуй еще раз".to_owned()),
            Media::video("menu"),
            InlineKeyboardMarkup::new(vec![row_btn_back()]),
        )
        .await?;
        return Ok(());
    }

    // Parse model details
    let name = lines[0].trim().to_owned();
    let price = match lines[1].trim().parse::<f64>() {
        Ok(p) => p,
        Err(_) => {
            ctx.send(
                Some("Не удалось распарсить цену, попробуй еще раз".to_owned()),
                Media::video("menu"),
                InlineKeyboardMarkup::new(vec![row_btn_back()]),
            )
            .await?;
            return Ok(());
        }
    };

    let production_cost = match lines[2].trim().parse::<f64>() {
        Ok(p) => p,
        Err(_) => {
            ctx.send(
                Some("Не удалось распарсить себестоимость, попробуй еще раз".to_owned()),
                Media::video("menu"),
                InlineKeyboardMarkup::new(vec![row_btn_back()]),
            )
            .await?;
            return Ok(());
        }
    };

    // Update existing model data with details
    {
        let mut data = MODEL_DATA.lock().await;
        if let Some(model_data) = data.get_mut(&ctx.user.id) {
            model_data.name = name;
            model_data.price = price;
            model_data.production_cost = production_cost;
        } else {
            ctx.send(
                Some("Ошибка: данные модели не найдены. Начни заново.".to_owned()),
                Media::video("menu"),
                InlineKeyboardMarkup::new(vec![row_btn_back()]),
            )
            .await?;
            return Ok(());
        }
    }

    let text = "Отлично! Теперь отправь мне медиа группу (фото/видео) с описанием модели в подписи";
    ctx.send(
        Some(text.to_owned()),
        Media::video("menu"),
        InlineKeyboardMarkup::new(vec![row_btn_back()]),
    )
    .await?;

    // Change step to handle media upload
    ctx.user.path.push_back(Step::Admin(AdminStep::AddModel));
    ctx.save_user().await
}

pub async fn on_add_model_media_group(ctx: Context, msg: Message) -> HandlerResult {
    let Some(media_group_id) = msg.media_group_id() else {
        return Ok(());
    };

    let media_group_id = media_group_id.to_owned();
    let mut is_photo = false;
    // Handle media group
    let file_id = if let Some(photo) = msg.photo().and_then(|sizes| sizes.last()) {
        is_photo = true;
        photo.file.id.clone()
    } else if let Some(video) = msg.video() {
        video.file.id.clone()
    } else {
        ctx.send(
            "Неподдерживаемый тип файла. Только фото и видео. Попробуй еще раз.".to_owned(),
            Media::video("menu"),
            InlineKeyboardMarkup::new(vec![row_btn_back()]),
        )
        .await?;
        return Ok(());
    };

    // Download the file
    let file = ctx.bot.get_file(&file_id).await?;
    let mut file_data = Vec::new();
    ctx.bot.download_file(&file.path, &mut file_data).await?;

    let media = if is_photo {
        Media::Photo(Uuid::new_v4().to_string())
    } else {
        Media::Video(Uuid::new_v4().to_string())
    };

    // Store in temporary collection
    {
        let mut groups = MEDIA_GROUPS.lock().await;
        let group = groups
            .entry(media_group_id.to_owned())
            .or_insert_with(Vec::new);
        group.push((media, file_data, msg.clone()));
    }

    tokio::spawn(
        async move {
            // Wait a bit to collect all files (Telegram usually sends them quickly)
            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

            // Process the group if this is the last message
            let files = {
                let mut groups = MEDIA_GROUPS.lock().await;
                if let Some(files) = groups.remove(&media_group_id) {
                    files
                } else {
                    return Ok(());
                }
            };

            // Get model data for this user
            let model_data = {
                let mut data = MODEL_DATA.lock().await;
                data.remove(&ctx.user.id)
            };

            let Some(mut model_data) = model_data else {
                ctx.send(
                    Some("Ошибка: данные модели не найдены. Начни заново.".to_owned()),
                    Media::video("menu"),
                    InlineKeyboardMarkup::new(vec![row_btn_back()]),
                )
                .await?;
                return Ok(());
            };

            // Extract description from the first message with caption
            let description = files
                .iter()
                .find_map(|(_, _, message)| message.caption())
                .unwrap_or("Описание не указано")
                .to_owned();

            model_data.description = description.clone();

            // Save all files to assets folder and save media IDs to database
            let mut medias = Vec::new();
            for (media, data, message) in &files {
                crate::media::save_media_to_file(media, data).await?;
                ctx.save_media_id_from_message(media, message).await?;
                medias.push(media.clone());
            }

            // Create product in database
            let product_id = ctx
                .db
                .create_product(
                    model_data.name.clone(),
                    model_data.category.clone(),
                    model_data.price,
                    model_data.production_cost,
                )
                .await?;

            // Create link product data
            ctx.db
                .create_link_product_data(product_id, model_data.name.clone(), medias, description)
                .await?;

            ctx.send(
                Some(format!(
                    "✅ Модель '{}' успешно добавлена!\n\
                    📂 Категория: {}\n\
                    💰 Цена: ${:.2}\n\
                    🏭 Себестоимость: ${:.2}\n\
                    🆔 ID продукта: {}",
                    model_data.name,
                    model_data.category,
                    model_data.price,
                    model_data.production_cost,
                    product_id.0
                )),
                Media::video("menu"),
                InlineKeyboardMarkup::new(vec![row_btn_back()]),
            )
            .await?;

            ctx.save_user().await
        }
        .in_current_span(),
    );

    Ok(())
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
async fn on_admin_mailing(ctx: Context) -> HandlerResult {
    let text = "Введи сообщение для рассылки";
    let markup = InlineKeyboardMarkup::new(vec![row_btn_back()]);
    ctx.send(Some(text.to_owned()), Media::video("menu"), markup)
        .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
async fn on_admin_mailing_message(mut ctx: Context, message: Message) -> HandlerResult {
    let users = ctx.db.get_all_users().await?;

    let users_len = users.len();
    let secs = users_len as f32 / 25.;

    ctx.delete_bot_message().await?;
    let status_msg = ctx
        .bot
        .send_message(
            ctx.chat_id(),
            format!("Бот начал рассылку, она займет примерно {secs:.0} секунд"),
        )
        .await?;

    let mut interval = tokio::time::interval(std::time::Duration::from_secs_f32(1. / 25.));
    interval.set_missed_tick_behavior(tokio::time::MissedTickBehavior::Skip);

    for user in users {
        interval.tick().await;

        let markup = if let Some(markup) = message.reply_markup() {
            markup
                .clone()
                .append_row(row_btn("СТАРТ", Callback::Restart))
        } else {
            InlineKeyboardMarkup::new(vec![row_btn("СТАРТ", Callback::Restart)])
        };

        match ctx
            .bot
            .copy_message(ChatId(user.id.0), ctx.chat_id(), message.id)
            .reply_markup(markup)
            .await
        {
            Ok(_) => {
                tracing::info!("Copied mailing message to user {}", user.id);
            }
            Err(err) => {
                tracing::error!("Failed to copy message to user {}: {err}", user.id);
            }
        }
    }

    ctx.bot.delete_message(ctx.chat_id(), status_msg.id).await?;
    ctx.delete_user_message().await?;

    ctx.bot
        .send_message(
            ctx.chat_id(),
            format!("Бот закончил рассылку по {users_len} пользователям"),
        )
        .await?;

    super::menu::on_menu(ctx).await
}

#[test]
fn test_promocodes_de() {
    let json = serde_json::json!({
        "id": 1,
        "text": "test",
        "value": 10,
        "kind": "sale",
        "start_date": "2025-01-01 12:53:00",
        "end_date": "2025-02-28 12:53:00",
        "activations": 0,
        "activations_max": 100,
    });
    let promo = serde_json::from_value::<crate::common::Promocode>(json).unwrap();
    assert_eq!(promo.activations, 0);
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_promocodes(ctx: Context, me: Me) -> HandlerResult {
    let all_promocodes = ctx.db.get_active_promocodes().await?;

    let text = all_promocodes
        .iter()
        .filter(|x| !x.hidden)
        .map(|x| {
            let value_display = match x.kind {
                PromocodeKind::Sale => format!("<i>-{}%</i>", x.value),
                PromocodeKind::Balance => format!("<i>+${} USD</i>", x.value),
            };

            let kind_display = match x.kind {
                PromocodeKind::Sale => "Скидка",
                PromocodeKind::Balance => "Баланс",
            };

            format!(
                "<b><a href=\"https://t.me/{}?start=promo-{}\">{}</a></b> {} ({}): <b>{}</b> активаций / {} макс\n\n",
                me.username(),
                x.id,
                x.text,
                value_display,
                kind_display,
                x.activations,
                x.activations_max
                    .map(|x| x.to_string())
                    .unwrap_or("∞".to_owned()),
            )
        })
        .join("\n");

    let markup = vec![
        row_btn(
            "Создать новый",
            Callback::Step(Step::Admin(AdminStep::CreatePromo)),
        ),
        row_btn_back(),
    ];

    ctx.send(
        Some(format!("Промокоды\n\n{}", text)),
        Media::video("menu"),
        InlineKeyboardMarkup::new(markup),
    )
    .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_create_promo(ctx: Context) -> HandlerResult {
    let text = "Введи: \n\
    * текст промокода\n\
    * тип промокода (sale / balance)\n\
    * процент скидки / доллары на баланс\n\
    * количество активаций (или прочерк '-')\n\
    * дата начала действия (либо через сколько начнется)\n\
    * дата конца действия (либо через сколько закончится после начала)\n\
    все с новой строки\n\
    Если количество активаций не указано, то будет бесконечно\n\n\
    Пример:\n\
    \n\
    <blockquote>ЛИЗНИ-ЯЙЦА-20\n\
    sale\n\
    20\n\
    100\n\
    2025-01-01 12:53:00\n\
    2025-02-28 12:53:00\n\
    </blockquote>\n\
    \n\
    Либо:\n\
    \n\
    <blockquote>ЛИЗНИ-ЯЙЦА-20\n\
    balance\n\
    50\n\
    -\n\
    2025-01-01 12:53:00\n\
    1year 15days 10hours 10minutes\n\
    </blockquote>";
    let markup = InlineKeyboardMarkup::new(vec![row_btn_back()]);
    ctx.send(Some(text.to_owned()), Media::video("menu"), markup)
        .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_create_promo_text(ctx: Context, message: Message, me: Me) -> HandlerResult {
    let Some(text) = message.text() else {
        ctx.bot
            .send_message(ctx.chat_id(), "Ожидал от тебя текст, попробуй еще раз")
            .await?;
        return Ok(());
    };
    let split = text.split('\n');
    let &[text, kind, value, activations, start_date, end_date] = split.collect_vec().as_slice()
    else {
        ctx.bot
            .send_message(
                ctx.chat_id(),
                "Ожидал что от тебя будет 6 строчек, попробуй еще раз",
            )
            .await?;
        return Ok(());
    };

    let kind = match kind.trim() {
        "sale" => PromocodeKind::Sale,
        "balance" => PromocodeKind::Balance,
        _ => {
            ctx.bot
                .send_message(
                    ctx.chat_id(),
                    "Не получилось спарсить тип промокода, попробуй еще раз",
                )
                .await?;
            return Ok(());
        }
    };

    let Ok(value) = value.trim().parse::<i32>() else {
        ctx.bot
            .send_message(
                ctx.chat_id(),
                "Не получилось спарсить процент скидки / доллары на баланс, попробуй еще раз",
            )
            .await?;
        return Ok(());
    };

    let activations = activations.trim();
    let activations = if activations == "-" {
        None
    } else {
        let Ok(activations) = activations.parse::<i32>() else {
            ctx.bot
                .send_message(
                    ctx.chat_id(),
                    "Не получилось спарсить количество активаций, попробуй еще раз",
                )
                .await?;
            return Ok(());
        };
        Some(activations)
    };

    let start_date = {
        if let Ok(start_date) = humantime::parse_rfc3339_weak(start_date) {
            chrono::DateTime::from_timestamp(
                start_date.duration_since(UNIX_EPOCH).unwrap().as_secs() as i64,
                0,
            )
            .unwrap()
        } else {
            let Ok(duration) = humantime::parse_duration(start_date) else {
                ctx.bot
                    .send_message(
                        ctx.chat_id(),
                        "Не получилось спарсить дату начала, попробуй еще раз",
                    )
                    .await?;
                return Ok(());
            };
            Utc::now() + duration
        }
    };

    let end_date = {
        if let Ok(end_date) = humantime::parse_rfc3339_weak(end_date) {
            chrono::DateTime::from_timestamp(
                end_date.duration_since(UNIX_EPOCH).unwrap().as_secs() as i64,
                0,
            )
            .unwrap()
        } else {
            let Ok(duration) = humantime::parse_duration(end_date) else {
                ctx.bot
                    .send_message(
                        ctx.chat_id(),
                        "Не получилось спарсить дату конца, попробуй еще раз",
                    )
                    .await?;
                return Ok(());
            };
            start_date + duration
        }
    };

    ctx.db
        .add_promocode(
            text.trim().to_owned(),
            value,
            kind,
            activations,
            start_date,
            end_date,
        )
        .await?;

    ctx.bot
        .send_message(ctx.chat_id(), "Промокод успешно добавлен")
        .await?;

    on_promocodes(ctx, me).await
}
