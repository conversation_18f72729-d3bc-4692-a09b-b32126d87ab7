use color_eyre::eyre::Context as _;
use teloxide::{
    dptree::{case, entry},
    types::{InlineKeyboardButton, InlineKeyboardMarkup, Me, Message, Update},
};
use tracing::instrument;

use crate::{
    common::{Callback, InvoiceId, Step},
    context::Context,
    external::crypto_pay::{self, Asset, CreateInvoiceParams, GetInvoiceParams},
    filter_cb, filter_message, filter_step,
    media::Media,
    row_btn, row_btn_back, row_btn_cart, Branch, HandlerResult,
};

// Database attribute key for minimum payment amount
const MIN_PAYMENT_AMOUNT_KEY: &str = "MIN_PAYMENT_AMOUNT";
// Default minimum payment amount if not set in database
const DEFAULT_MIN_PAYMENT_AMOUNT: f64 = 5.0;

#[rustfmt::skip]
pub fn branch() -> Branch {
    entry()
        .map(|ctx: Context| ctx.current_step)
        .branch(case![Step::Profile]
            .branch(filter_step()
                .branch(case![Step::AddBalance].endpoint(on_add_balance))
                .branch(case![Step::Cart].endpoint(super::cart::on_cart))
            )
            .branch(filter_cb()
                .branch(case![Callback::Payed(id)].endpoint(check_payment))
                .branch(case![Callback::CancelPay(id)].endpoint(cancel_payment))
            )
        )
        .branch(case![Step::AddBalance]
            .branch(filter_message()
                .endpoint(on_add_balance_msg)
            )
            .branch(filter_cb()
                .branch(case![Callback::Payed(id)].endpoint(check_payment))
                .branch(case![Callback::CancelPay(id)].endpoint(cancel_payment))
            )
        )

}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_profile(ctx: Context) -> HandlerResult {
    tracing::debug!("on_profile");

    let mut markup = vec![];

    let text = if let Some(pending_invoice) = ctx.user.pending_invoice_id {
        markup.push(row_btn("Я оплатил", Callback::Payed(pending_invoice)));
        markup.push(row_btn(
            "Отменить оплату",
            Callback::CancelPay(pending_invoice),
        ));

        "!!!Инвойс ожидает оплаты!!!\n\n"
    } else {
        markup.push(row_btn(
            "Пополнить баланс",
            Callback::Step(Step::AddBalance),
        ));
        ""
    };

    let total_spent = ctx.db.get_user_total_spent(ctx.user.id).await?;

    let text = format!(
        "<b>🦍Профиль</b>\n\
        \n\
        {text}\
        🆔: <code>{}</code>\n\
        <b>💸Баланс</b>: {:.2}$\n\
        \n\
        <b>💰Всего куплено на</b>: {total_spent:.2}$",
        ctx.user.id, ctx.user.balance
    );

    markup.push(row_btn_cart());
    markup.push(row_btn_back());

    ctx.send(
        Some(text),
        Media::video("profile"),
        InlineKeyboardMarkup::new(markup),
    )
    .await?;
    ctx.save_user().await?;

    Ok(())
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_add_balance(ctx: Context) -> HandlerResult {
    tracing::debug!("on_add_balance");

    // Get minimum payment amount from database or use default
    let min_amount = ctx
        .db
        .get_attribute(MIN_PAYMENT_AMOUNT_KEY)
        .await
        .unwrap_or(None)
        .and_then(|v| v.parse::<f64>().ok())
        .unwrap_or(DEFAULT_MIN_PAYMENT_AMOUNT);

    let text =
        format!("<b>Введи сумму для пополнения:</b>\n<i>Минимальная сумма: {min_amount}$</i>");

    let markup = InlineKeyboardMarkup::new(vec![row_btn_back()]);

    ctx.send(Some(text.to_owned()), Media::video("profile"), markup)
        .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_add_balance_msg(mut ctx: Context, message: Message) -> HandlerResult {
    tracing::debug!("on_add_balance_text");
    let text = message.text().expect("Should be text");
    let text = text.trim();

    if text.is_empty() {
        unimplemented!("empty text");
    }

    ctx.delete_bot_message().await?;

    let amount = text.parse::<f64>().wrap_err("Failed to parse amount")?;

    // Get minimum payment amount from database or use default
    let min_amount = ctx
        .db
        .get_attribute(MIN_PAYMENT_AMOUNT_KEY)
        .await
        .unwrap_or(None)
        .and_then(|v| v.parse::<f64>().ok())
        .unwrap_or(DEFAULT_MIN_PAYMENT_AMOUNT);

    // Check if the amount is at least the minimum required
    if amount < min_amount {
        let error_text = format!("<b>Минимальная сумма пополнения: {min_amount}$</b>");
        let markup = InlineKeyboardMarkup::new(vec![row_btn_back()]);

        ctx.send(Some(error_text), Media::video("profile"), markup)
            .await?;
        return Ok(());
    }

    let invoice = ctx
        .crypto_pay
        .create_invoice(&CreateInvoiceParams {
            currency_type: crypto_pay::CurrencyType::Crypto,
            asset: Some(Asset::USDT),
            fiat: None,
            accepted_assets: None,
            amount: format!("{amount:.1}"),
            description: None,
            hidden_message: None,
            paid_btn_name: None,
            paid_btn_url: None,
            payload: None,
            allow_comments: Some(true),
            allow_anonymous: Some(true),
            expires_in: Some("7200".to_owned()),
        })
        .await?;

    tracing::debug!("invoice created: {:?}", invoice);

    let invoice_id = invoice.invoice_id;
    ctx.user.pending_invoice_id = Some(InvoiceId(invoice_id));
    ctx.save_user().await?;

    let text = format!(
        "💸 Пополнение баланса

💳 К оплате: <b>{amount}$</b>
⏳ Время на оплату: <b>120 минут</b>

<i>1️⃣ Нажмите на кнопку для оплаты</i>
<i>2️⃣ После оплаты нажми \"Я оплатил\"</i>",
    );

    let url = invoice.bot_invoice_url.parse().unwrap();
    ctx.send(
        Some(text),
        Media::video("profile"),
        InlineKeyboardMarkup::new(vec![
            vec![InlineKeyboardButton::url("Перейти к оплате", url)],
            row_btn("Я оплатил", Callback::Payed(InvoiceId(invoice_id))),
            row_btn("Отмена", Callback::CancelPay(InvoiceId(invoice_id))),
        ]),
    )
    .await?;

    ctx.save_user().await?;
    Ok(())
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn cancel_payment(
    mut ctx: Context,
    invoice_id: InvoiceId,
    update: Update,
    me: Me,
) -> HandlerResult {
    tracing::debug!("cancel payment");

    if ctx.user.pending_invoice_id.is_none() {
        tracing::warn!("cancel payment: no pending invoice");
        ctx.answer_cq("Инвойс устарел!").await;
    } else {
        ctx.crypto_pay.delete_invoice(invoice_id.0).await?;
        ctx.user.pending_invoice_id = None;
        ctx.answer_cq("Оплата отменена!").await;
    }

    crate::on_back(ctx, update, me).await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn check_payment(
    mut ctx: Context,
    invoice_id: InvoiceId,
    update: Update,
    me: Me,
) -> HandlerResult {
    tracing::debug!("check payment");

    if ctx.user.pending_invoice_id.is_none() {
        tracing::warn!("check payment: no pending invoice");
        ctx.answer_cq("Инвойс устарел!").await;
        return crate::on_back(ctx, update, me).await;
    }

    let invoice = ctx
        .crypto_pay
        .get_invoices(&GetInvoiceParams {
            invoice_ids: invoice_id.0.to_string(),
            count: Some(1),
            asset: None,
            fiat: None,
            status: None,
            offset: None,
        })
        .await?;

    let Some(invoice) = invoice.first() else {
        ctx.answer_cq("Не вижу оплаты! Попробуй через минуту").await;
        return Ok(());
    };

    match invoice.status {
        crypto_pay::InvoiceStatus::Paid => {
            let amount: f64 = invoice.amount.parse().unwrap();
            ctx.user.balance += amount;
            ctx.answer_cq("Оплата прошла!").await;
        }
        crypto_pay::InvoiceStatus::Expired => {
            ctx.answer_cq("Инвойс устарел, попробуй оплатить еще раз через профиль!")
                .await;
            return on_profile(ctx).await;
        }
        crypto_pay::InvoiceStatus::Active => {
            ctx.answer_cq("Криптобот говорит что ты еще не оплатил инвойс, шалун!")
                .await;
            return Ok(());
        }
    }

    tracing::debug!("invoice payed: {:?}", invoice);

    ctx.user.pending_invoice_id = None;
    ctx.save_user().await?;

    crate::on_back(ctx, update, me).await
}
