use std::{collections::HashMap, ops::Deref};

use color_eyre::eyre::{Context as _, ContextCompat};
use teloxide::{
    dptree::{case, entry},
    types::InlineKeyboardMarkup,
};
use tracing::instrument;

use crate::{
    btn,
    common::{
        Callback, CatalogEntry, CatalogNavKind, Directory, LinkProductData, ProductId, ProductKind,
    },
    context::Context,
    db::Json,
    media::Media,
    row_btn, row_btn_back, row_btn_cart, row_btn_url, Branch, BranchExt, HandlerResult, Step,
};

crate::string_enum!(
    pub enum AccountGeo {
        Ru,
        Eu,
        Kz,
    }
);

crate::string_enum!(
    pub enum CatalogStep {
        Directory(Directory),
        Product(ProductId),
        ProductCategory(String),
    }
);

fn cstep_cq(step: CatalogStep) -> Callback {
    Callback::Step(Step::Catalog(step))
}

#[rustfmt::skip]
pub fn branch() -> Branch {
    entry()
        .map(|ctx: Context| ctx.current_step)
        .filter_map(|step: Step| match step {
            Step::Catalog(a) => Some(a),
            _ => None,
        })
        .branch(case![CatalogStep::Directory(loc)].filter_catalog_step()
            .branch(case![CatalogStep::Directory(loc)].endpoint(on_catalog))
            .branch(case![CatalogStep::Product(id)].endpoint(on_product))
            .branch(case![CatalogStep::ProductCategory(id)].endpoint(on_product_category))
        )
        
        .branch(case![CatalogStep::Product(id)].filter_cb()
            // .branch(case![Callback::Plus(id)].endpoint(add_standalone_product))
            // .branch(case![Callback::Minus(id)].endpoint(sub_standalone_product))
        )
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_catalog(mut ctx: Context, loc: Directory) -> HandlerResult {
    tracing::debug!("on_catalog: {loc:?}");

    let catalog = ctx.db.get_catalog().await?;

    // let catalog = serde_json::json!(
    //     [
    //         {
    //             "id": "root",
    //             "media": "accounts",
    //             "caption": null,
    //             "nav": [
    //                 {
    //                     "btn": "🗂 Канал-каталог анкет",
    //                     "url": "https://example.com",
    //                 },
    //                 {
    //                     "btn": "🔐 Пак кружков (обезличенные)",
    //                     "product_category": "pack",
    //                 },
    //                 {
    //                     "btn": "🧚🏻‍♀️ Модели классика",
    //                     "loc": "classic",
    //                 },
    //                 {
    //                     "btn": "🔞 Модели onlyfans",
    //                     "loc": "onlyfans",
    //                 },
    //                 {
    //                     "btn": "🧖🏻‍♀️ Модели домашние",
    //                     "loc": "home",
    //                 }
    //             ]
    //         },
    //         {
    //             "id": "classic",
    //             "media": "accounts",
    //             "caption": null,
    //             "nav": [
    //                 {
    //                     "btn": "малый пак",
    //                     "product_category": "classic-small",
    //                 },
    //                 {
    //                     "btn": "средний пак",
    //                     "product_category": "classic-medium",
    //                 },
    //                 {
    //                     "btn": "большой пак",
    //                     "product_category": "classic-big",
    //                 }
    //             ]
    //         },
    //         {
    //             "id": "onlyfans",
    //             "media": "accounts",
    //             "caption": null,
    //             "nav": [
    //                 {
    //                     "btn": "малый пак",
    //                     "product_category": "onlyfans-small",
    //                 },
    //                 {
    //                     "btn": "средний пак",
    //                     "product_category": "onlyfans-medium",
    //                 },
    //                 {
    //                     "btn": "большой пак",
    //                     "product_category": "onlyfans-big",
    //                 }
    //             ]
    //         },
    //         {
    //             "id": "home",
    //             "media": "accounts",
    //             "caption": null,
    //             "nav": [
    //                 {
    //                     "btn": "малый пак",
    //                     "product_category": "home-small",
    //                 },
    //                 {
    //                     "btn": "средний пак",
    //                     "product_category": "home-medium",
    //                 },
    //                 {
    //                     "btn": "большой пак",
    //                     "product_category": "home-big",
    //                 }
    //             ]
    //         },
    //     ]
    // );

    let entry = catalog.iter().find(|x| x.id == loc).unwrap();

    tracing::debug!("entry: {:?}", entry);

    let markup = InlineKeyboardMarkup::new(
        entry
            .nav
            .as_ref()
            .expect("nav must be there for catalog")
            .iter()
            .map(|x| match &x.kind {
                CatalogNavKind::Directory { dir } => {
                    row_btn(&x.btn, cstep_cq(CatalogStep::Directory(dir.clone())))
                }
                CatalogNavKind::ProductCategory { product_category } => row_btn(
                    &x.btn,
                    cstep_cq(CatalogStep::ProductCategory(product_category.clone())),
                ),
                CatalogNavKind::Url { url } => row_btn_url(&x.btn, url),
            })
            .chain(vec![row_btn_cart(), row_btn_back()]),
    );

    // todo: get media from str

    ctx.user
        .path
        .push_back(Step::Catalog(CatalogStep::Directory(loc.clone())));
    ctx.send(
        entry.description.clone(),
        Media::video(entry.media.clone()),
        markup,
    )
    .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_product_category(ctx: Context, product_category_name: String) -> HandlerResult {
    tracing::debug!("on_product_category: {product_category_name:?}");

    let category = ctx
        .db
        .get_product_category(&product_category_name)
        .await?
        .wrap_err("Failed to find product category")?;

    let products = ctx.db.get_products_by_category(&category.id).await?;

    let markup = InlineKeyboardMarkup::new(
        products
            .iter()
            .map(|x| row_btn(&x.name, cstep_cq(CatalogStep::Product(x.id))))
            .chain(vec![row_btn_cart(), row_btn_back()]),
    );

    ctx.send(
        category.description,
        Media::video(category.media.clone()),
        markup,
    )
    .await?;
    ctx.save_user().await
}

pub async fn on_product(mut ctx: Context, product_id: ProductId) -> HandlerResult {
    tracing::debug!("on_product: {product_id:?}");

    let product = ctx
        .db
        .get_product_description(product_id)
        .await?
        .expect("Product should exist in db");

    match product.kind {
        ProductKind::Link => {
            // is unique

            let data: LinkProductData = ctx
                .db
                .get_link_product_data(product.id)
                .await?
                .expect("Unique product data should exist in db");

            let markup = InlineKeyboardMarkup::new(vec![
                row_btn("Добавить в корзину", Callback::Plus(product.id)),
                row_btn_cart(),
                row_btn_back(),
            ]);

            ctx.send(Some(data.description), data.medias.inner(), markup)
                .await?;
        }
    }

    // model name
    // 3 photos
    // one video
    // description

    // products_data table
    // shop_id
    // id (unique per unit)
    // category
    // product_id (home-small, pack etc)
    // kind (how to handle this, for now only links)
    // data (json)

    ctx.save_user().await
}

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn on_accounts(ctx: Context) -> HandlerResult {
//     tracing::debug!("on_accounts");
//     let text = "<b>Аккаунты</b>
// ╰ 📞Аккаунты зарегистрированые на физические сим-карты.
// ╰ 🔢Отлежка (Возраст) - прошедшее время с момента регистрации аккаунта.
// ╰ ⛔️Подключаетесь без прокси, замене не подлежит.
// ";

//     let markup = InlineKeyboardMarkup::new(vec![
//         row_btn(
//             "Telegram (Россия)",
//             Callback::Step(Step::Catalog(CatalogStep::AccountsTg(AccountGeo::Ru))),
//         ),
//         row_btn(
//             "Telegram (Европа)",
//             Callback::Step(Step::Catalog(CatalogStep::AccountsTg(AccountGeo::Eu))),
//         ),
//         row_btn(
//             "ВКонтакте",
//             Callback::Step(Step::Catalog(CatalogStep::AccountsVk)),
//         ),
//         row_btn(
//             "Instagram",
//             Callback::Step(Step::Catalog(CatalogStep::AccountsInsta)),
//         ),
//         row_btn_cart(),
//         row_btn_back(),
//     ]);

//     ctx.send(Some(text.to_owned()), Media::Accounts, markup)
//         .await?;
//     ctx.save_user().await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn on_accounts_tg(ctx: Context, geo: AccountGeo) -> HandlerResult {
//     tracing::debug!("on_accounts_{geo:?}");

//     let text = format!(
//         "<b>Аккаунты Telegram ({})</b>
// ╰ 📞Аккаунты Telegram зарегистрированые на физические сим-карты.
// ╰ 🔢Отлежка (Возраст) - прошедшее время с момента регистрации аккаунта.
// ╰ ⛔️Подключаетесь без прокси, замене не подлежит.",
//         match geo {
//             AccountGeo::Ru => "Россия",
//             AccountGeo::Eu => "Европа",
//             AccountGeo::Kz => "Казахстан",
//         }
//     );

//     let mut products = match geo {
//         AccountGeo::Ru => ctx.db.get_products_by_path("Аккаунты/ФИЗ RU").await,
//         AccountGeo::Eu => ctx.db.get_products_by_path("Аккаунты/ФИЗ EU").await,
//         AccountGeo::Kz => ctx.db.get_products_by_path("Аккаунты/ФИЗ KZ").await,
//     }?;
//     products.sort_by(|a, b| b.price.total_cmp(&a.price));

//     let mut in_stock = HashMap::new();
//     for product in &products {
//         let count = ctx.db.how_much_in_stock(&product.id, product.kind).await?;
//         in_stock.insert(&product.id, count >= product.qty);
//     }

//     let markup = InlineKeyboardMarkup::new(
//         products
//             .iter()
//             .map(|x| x.button(in_stock[&&x.id]))
//             .chain(vec![row_btn_cart(), row_btn_back()]),
//     );

//     ctx.send(text.to_owned(), Media::AccountsTg, markup).await?;
//     ctx.save_user().await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn on_traffic(ctx: Context) -> HandlerResult {
//     tracing::debug!("on_base");

//     let markup = InlineKeyboardMarkup::new(vec![
//         row_btn(
//             "📁База Telegram",
//             Callback::Step(Step::Catalog(CatalogStep::TrafficTg)),
//         ),
//         row_btn(
//             "📁База ВКонтакте",
//             Callback::Step(Step::Catalog(CatalogStep::TrafficVk)),
//         ),
//         row_btn_cart(),
//         row_btn_back(),
//     ]);

//     ctx.send(None, Media::Traffic, markup).await?;
//     ctx.save_user().await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn on_traffic_tg(ctx: Context) -> HandlerResult {
//     tracing::debug!("on_base_tg");
//     let text = "<b>📁База Telegram</b>

// ╰ 📁Трафик собранный в telegram для вашего инвайта и не только.
// ╰ 💇‍♀️Платежеспособная, женская, целевая аудитория.
// ╰ 📨Формат выдачи username|txt.";

//     let mut products = ctx.db.get_products_by_path("База/ТГ").await?;
//     products.sort_by(|x, y| y.qty.partial_cmp(&x.qty).unwrap());

//     let mut in_stock = HashMap::new();
//     for product in &products {
//         let count = ctx.db.how_much_in_stock(&product.id, product.kind).await?;
//         in_stock.insert(&product.id, count >= product.qty);
//     }

//     let markup = InlineKeyboardMarkup::new(
//         products
//             .iter()
//             .map(|x| x.button(in_stock[&&x.id]))
//             .chain(vec![
//                 // row_btn_url("Объем свыше обговаривается лично", ADMIN_URL),
//                 row_btn_cart(),
//                 row_btn_back(),
//             ]),
//     );

//     ctx.send(Some(text.to_owned()), Media::TrafficTg, markup)
//         .await?;
//     ctx.save_user().await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn on_traffic_vk(ctx: Context) -> HandlerResult {
//     tracing::debug!("on_base_tg");
//     let text = "<b>📁База ВКонтакте</b>

// ╰ 📁Трафик собранный в вконтакте для вашего инвайта и не только.
// ╰ 💇‍♀️Платежеспособная, женская, целевая аудитория.
// ╰ 📨Формат выдачи username|txt.";

//     let mut products = ctx.db.get_products_by_path("База/ВК").await?;
//     products.sort_by(|x, y| y.qty.partial_cmp(&x.qty).unwrap());

//     let mut in_stock = HashMap::new();
//     for product in &products {
//         let count = ctx.db.how_much_in_stock(&product.id, product.kind).await?;
//         in_stock.insert(&product.id, count >= product.qty);
//     }

//     let markup = InlineKeyboardMarkup::new(
//         products
//             .iter()
//             .map(|x| x.button(in_stock[&&x.id]))
//             .chain(vec![
//                 // row_btn_url("Объем свыше обговаривается лично", ADMIN_URL),
//                 row_btn_cart(),
//                 row_btn_back(),
//             ]),
//     );

//     ctx.send(Some(text.to_owned()), Media::TrafficVk, markup)
//         .await?;
//     ctx.save_user().await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn on_accounts_vk(ctx: Context) -> HandlerResult {
//     tracing::debug!("on_vk");

//     let text = "<b>Аккаунты ВКонтакте</b>
// ╰ 📞Аккаунты ВКонтакте зарегистрированые на физические сим-карты.
// ╰ 🔢Отлежка (Возраст) - прошедшее время с момента регистрации аккаунта.
// ╰ ⛔️Подключаетесь без прокси, замене не подлежит.";

//     let products = ctx.db.get_products_by_path("Аккаунты/ВК").await?;

//     let mut in_stock = HashMap::new();
//     for product in &products {
//         let count = ctx.db.how_much_in_stock(&product.id, product.kind).await?;
//         in_stock.insert(&product.id, count >= product.qty);
//     }

//     let markup = InlineKeyboardMarkup::new(
//         products
//             .iter()
//             .map(|x| x.button(in_stock[&&x.id]))
//             .chain(vec![row_btn_cart(), row_btn_back()]),
//     );

//     ctx.send(text.to_owned(), Media::AccountsVk, markup).await?;
//     ctx.save_user().await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn on_accounts_insta(ctx: Context) -> HandlerResult {
//     tracing::debug!("on_insta");

//     let text = "<b>Аккаунты Instagram</b>
// ╰ 📞Аккаунты Instagram зарегистрированые на физические сим-карты.
// ╰ 🔢Отлежка (Возраст) - прошедшее время с момента регистрации аккаунта.
// ╰ ⛔️Подключаетесь без прокси, замене не подлежит.";

//     let products = ctx.db.get_products_by_path("Аккаунты/Instagram").await?;

//     let mut in_stock = HashMap::new();
//     for product in &products {
//         let count = ctx.db.how_much_in_stock(&product.id, product.kind).await?;
//         in_stock.insert(&product.id, count >= product.qty);
//     }

//     let markup = InlineKeyboardMarkup::new(
//         products
//             .iter()
//             .map(|x| x.button(in_stock[&&x.id]))
//             .chain(vec![row_btn_cart(), row_btn_back()]),
//     );

//     ctx.send(text.to_owned(), Media::AccountsInst, markup)
//         .await?;
//     ctx.save_user().await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn on_standalone_product(ctx: Context, product_id: DbProductId) -> HandlerResult {
//     tracing::debug!("on_generic_product");
//     let product = ctx
//         .db
//         .get_product(&product_id)
//         .await?
//         .expect("Product should exist in db");

//     let amount = ctx.user.product_amount(&product_id);

//     let text = format!("Добавить к корзине {}", product.name);

//     let markup = InlineKeyboardMarkup::new(vec![
//         vec![
//             btn(
//                 "-",
//                 if amount > 0 {
//                     Callback::Minus(product_id.clone())
//                 } else {
//                     Callback::Empty
//                 },
//             ),
//             btn(&format!("{}", amount), Callback::Empty),
//             btn("+", Callback::Plus(product_id.clone())),
//         ],
//         row_btn_cart(),
//         row_btn_back(),
//     ]);

//     let media = match product.kind {
//         DbProductKind::AccountTg => Media::AccountsTg,
//         DbProductKind::TrafficTg => Media::TrafficTg,
//         DbProductKind::TrafficVk => Media::TrafficVk,
//         DbProductKind::AccountInsta => Media::AccountsInst,
//         DbProductKind::AccountVk => Media::AccountsVk,
//     };

//     ctx.send(Some(text), media, markup).await?;
//     ctx.save_user().await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// async fn sub_standalone_product(mut ctx: Context, product_id: DbProductId) -> HandlerResult {
//     tracing::debug!("on_minus");
//     if let Some(amount) = ctx.user.cart.get(&product_id).copied() {
//         if amount == 1 {
//             ctx.user.cart.remove(&product_id);
//         } else {
//             *ctx.user.cart.get_mut(&product_id).unwrap() -= 1;
//         }
//     }

//     on_standalone_product(ctx, product_id).await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// async fn add_standalone_product(mut ctx: Context, product_id: DbProductId) -> HandlerResult {
//     tracing::debug!("on_plus");

//     let Some(product) = ctx.db.get_product(&product_id).await? else {
//         tracing::error!("Attempt to plus non existing product");
//         return Ok(());
//     };

//     let cur_cart_amount = ctx.user.cart.get(&product_id).copied().unwrap_or_default();

//     let in_stock = ctx.db.how_much_in_stock(&product_id, product.kind).await?;

//     if cur_cart_amount + 1 > in_stock as u32 {
//         ctx.answer_cq("Больше нет в наличии").await;
//         return Ok(());
//     }

//     ctx.user
//         .cart
//         .insert(product_id.clone(), cur_cart_amount + 1);

//     on_standalone_product(ctx, product_id).await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// async fn add_traffic(mut ctx: Context, product_id: DbProductId) -> HandlerResult {
//     tracing::debug!("on_add_traffic_product");
//     // Remove all Traffic products from cart
//     // They are unique in a way
//     let product = ctx
//         .db
//         .get_product(&product_id)
//         .await?
//         .expect("Product should exist in db");

//     for product in ctx.db.get_products_by_path(&product.path).await? {
//         if ctx.user.cart.remove(&product.id).is_some() {
//             ctx.answer_cq(&format!("Удалено из корзины: {}", product.name))
//                 .await;
//         }
//     }

//     ctx.user.cart.insert(product_id, 1);
//     ctx.answer_cq("Добавлено в корзину!").await;

//     ctx.user.path.push_back(Step::Cart);
//     super::cart::on_cart(ctx).await
// }

// #[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
// pub async fn remove_traffic(mut ctx: Context, product_id: DbProductId) -> HandlerResult {
//     tracing::debug!("on_delete_traffic_product");

//     ctx.user.cart.remove(&product_id);
//     ctx.answer_cq("Удалено из корзины!").await;

//     super::cart::on_cart(ctx).await
// }
