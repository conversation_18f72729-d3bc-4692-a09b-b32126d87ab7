use std::collections::HashSet;

use teloxide::{
    dptree::{case, entry},
    prelude::Request,
    types::InlineKeyboardMarkup,
};
use tracing::instrument;

use crate::{
    common::ProductKind, context::Context, filter_cb, filter_step, media::Media, row_btn,
    row_btn_back, tiposhop::catalog::CatalogStep, Branch, Callback, HandlerResult, Step,
};

#[rustfmt::skip]
pub fn branch() -> Branch {
    entry()
        .map(|ctx: Context| ctx.current_step)
        .branch(case![Step::Cart]
            .branch(filter_step()
                .branch(case![Step::Checkout].endpoint(on_checkout))
                .branch(case![Step::AddBalance].endpoint(super::profile::on_add_balance))
            )
        )
        .branch(case![Step::Checkout]
            .chain(filter_cb())
                .branch(case![Callback::Payed(id)].endpoint(super::profile::check_payment))
                .branch(case![Callback::CancelPay(id)].endpoint(super::profile::cancel_payment))
        )
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_cart(ctx: Context) -> HandlerResult {
    tracing::debug!("on_cart");
    let mut markup = vec![];
    let mut total = 0.;

    let promo = ctx.db.get_active_promo(ctx.user.id).await?;
    let raw_discount = promo.as_ref().map(|x| x.value);
    let discount = promo.as_ref().map(|x| x.value).unwrap_or(0) as f64 / 100.;

    let maybe_discount = raw_discount
        .map(|x| format!(" (-{}%)", x))
        .unwrap_or_default();

    for product_id in ctx.user.cart.iter() {
        let product = ctx
            .db
            .get_product_description(*product_id)
            .await?
            .expect("Product should exist in db");

        let name = format!("{} - {:.0}$", product.name, product.price);

        let callback = Callback::Step(Step::Catalog(CatalogStep::Product(product.id)));

        markup.push(row_btn(&name, callback));
        total += product.price;
    }

    let total = total - (total * discount);

    let text = if !ctx.user.cart.is_empty() {
        if total <= ctx.user.balance && total > 0.001 {
            markup.push(row_btn(
                &format!("Оплатить: {total:.1}${maybe_discount}",),
                Callback::Step(Step::Checkout),
            ));
        } else {
            markup.push(row_btn(
                &format!("💰 Пополнить баланс {total:.1}${maybe_discount}(CRYPTOBOT)",),
                Callback::Step(Step::AddBalance),
            ));
        }

        "⛔️Для того чтобы удалить позицию, нажмите на нее."
    } else {
        "Ваша корзина пуста"
    };

    let text = if let Some(promo) = promo {
        format!(
            "{text}\n\nАктивный промокод: <b>{}</b> (-{}%)",
            promo.text, promo.value
        )
    } else {
        text.to_owned()
    };

    markup.push(row_btn_back());

    ctx.send(
        Some(text),
        Media::video("cart"),
        InlineKeyboardMarkup::new(markup),
    )
    .await?;
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_checkout(mut ctx: Context) -> HandlerResult {
    tracing::debug!("on_pay");

    let promo = ctx.db.get_active_promo(ctx.user.id).await?;
    let discount = promo.as_ref().map(|x| x.value).unwrap_or(0) as f64 / 100.;

    let mut products = Vec::with_capacity(ctx.user.cart.len());
    for product_id in ctx.user.cart.iter() {
        let product = ctx
            .db
            .get_product_description(*product_id)
            .await?
            .expect("Product should exist in db");
        products.push(product);
    }

    let pre_total: f64 = products.iter().map(|x| x.price).sum();
    let pre_total = pre_total - pre_total * discount;

    if ctx.user.balance < pre_total {
        ctx.answer_cq("Недостаточно средств для оплаты!").await;
        return Ok(());
    }

    let mut total = 0.;
    let mut bought = HashSet::new();

    for product in products {
        let price = product.price;

        match product.kind {
            ProductKind::Link => {
                tracing::debug!("Bought link: {:?}", product);
                bought.insert(product);
            } // DbProductKind::AccountTg => {
              //     // accounts are in different messages so push them separately
              //     bought.insert(product.clone(), 0);
              //     for _ in 0..*amount {
              //         if let Some(account) = ctx.db.get_account_tg_to_sell(&product.id).await? {
              //             accounts.push((product.clone(), account));
              //             *bought.get_mut(&product).unwrap() += 1;
              //         } else {
              //             tracing::warn!("No accounts to buy");
              //             continue 'product;
              //         }
              //     }
              // }
              // DbProductKind::TrafficTg => {
              //     let b = ctx.db.get_traffic_tg_to_sell(product.qty).await?;
              //     if b.is_empty() {
              //         tracing::warn!("No traffic tg to buy");
              //         continue;
              //     }
              //     bought.insert(product.clone(), product.qty as u32);
              //     traffic_tg = b;
              // }
              // DbProductKind::TrafficVk => {
              //     let b = ctx.db.get_traffic_vk_to_sell(product.qty).await?;
              //     if b.is_empty() {
              //         tracing::warn!("No traffic vk to buy");
              //         continue;
              //     }
              //     bought.insert(product.clone(), product.qty as u32);
              //     traffic_vk = b;
              // }
              // DbProductKind::AccountVk => {
              //     let b = ctx
              //         .db
              //         .get_account_vk_to_sell(&product.id, *amount as i32)
              //         .await?;
              //     if b.is_empty() {
              //         tracing::warn!("No vk to buy");
              //         continue;
              //     }
              //     bought.insert(product.clone(), *amount);
              //     vk.extend(b.into_iter().map(|x| (product.clone(), x.file)));
              // }
              // DbProductKind::AccountInsta => {
              //     let b = ctx
              //         .db
              //         .get_account_insta_to_sell(&product.id, *amount as i32)
              //         .await?;
              //     if b.is_empty() {
              //         tracing::warn!("No insta to buy");
              //         continue;
              //     }
              //     bought.insert(product.clone(), *amount);
              //     insta.extend(b.into_iter().map(|x| (product.clone(), x.file)));
              // }
        }
        total += price;
    }

    let total = total - total * discount;

    tracing::debug!("total usd: {}", total);

    if total <= 0.01 {
        ctx.answer_cq("Ничего не куплено!").await;
        return Ok(());
    }

    ctx.user.balance -= total;
    ctx.user.active_promocode_id = None;
    ctx.user.cart.clear();
    ctx.answer_cq("Средства списаны!").await;

    // REGISTER PURCHASES
    for product in &bought {
        let price = product.price;

        let price = price - price * discount;
        ctx.register_purchase(product.id, &promo, price, product.production_cost)
            .await?;
    }

    if let Err(err) = ctx.delete_bot_message().await {
        tracing::error!("Failed to delete message on checkout: {err}");
    }

    ctx.notify_db_bought(bought, promo.clone(), ctx.user.referred_by);

    super::menu::on_menu(ctx).await
}
