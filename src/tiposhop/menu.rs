use color_eyre::eyre::{Context as _, ContextCompat};
use teloxide::{
    dptree::{case, entry},
    prelude::Requester,
    types::{ChatId, InlineKeyboardMarkup, Message},
};
use tracing::instrument;

use crate::{
    common::{Directory, PromocodeId},
    context::Context,
    db, filter_message, filter_step,
    media::Media,
    row_btn, row_btn_cart, row_btn_if, row_btn_url,
    tiposhop::{admin::AdminStep, catalog::CatalogStep},
    Branch, BranchExt, Callback, HandlerResult, Step, UserId, ADMIN_URL,
};

#[rustfmt::skip]
pub fn branch() -> Branch {
    entry()
        .map(|ctx: Context| ctx.current_step)
        .branch(case![Step::Default]
            .branch(filter_step()
                .branch(case![Step::Menu].endpoint(on_menu))
            )
            .branch(filter_message()
                .endpoint(on_start_message)
            )
        )
        .branch(case![Step::Menu].filter_step()
            .branch(case![Step::Catalog(a)].map(Directory::default).endpoint(super::catalog::on_catalog))
            .branch(case![Step::Profile].endpoint(super::profile::on_profile))
            .branch(case![Step::Referral].endpoint(super::referral::on_referral))
            .branch(case![Step::Cart].endpoint(super::cart::on_cart))
            .branch(case![Step::Admin(s)].endpoint(super::admin::cb))
        )
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_restart(mut ctx: Context) -> HandlerResult {
    ctx.bot_message_id = None;
    on_menu(ctx).await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_menu(mut ctx: Context) -> HandlerResult {
    tracing::debug!("on_start");
    let markup = InlineKeyboardMarkup::new(vec![
        row_btn(
            "🛒Каталог",
            Callback::Step(Step::Catalog(CatalogStep::Directory(Directory::default()))),
        ),
        row_btn("🦍Профиль", Callback::Step(Step::Profile)),
        row_btn("👥Рефералы", Callback::Step(Step::Referral)),
        row_btn_if(
            "Админка",
            Callback::Step(Step::Admin(AdminStep::Default)),
            ctx.user.is_admin,
        ),
        row_btn_url("📞Поддержка", ADMIN_URL),
        row_btn_cart(),
    ]);
    ctx.send(None, Media::video("menu"), markup).await?;

    ctx.user.path.clear();
    ctx.user.path.push_back(Step::Menu);
    ctx.save_user().await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn on_start_message(mut ctx: Context, msg: Message) -> HandlerResult {
    tracing::debug!("on_start_text");
    let chat_id = msg.chat.id;

    ctx.bot.delete_message(chat_id, msg.id).await?;

    let text = msg.text().unwrap();
    if let Some(sub) = text.strip_prefix("/start ") {
        try_handle_start_ext(&mut ctx, sub).await?;
    }

    on_menu(ctx).await
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
pub async fn try_handle_start_ext(ctx: &mut Context, text: &str) -> HandlerResult {
    if let Some(combo) = text.strip_prefix("combo-") {
        tracing::debug!("on_start_combo: {combo}");

        let mut split = combo.split('-');
        let referral = split
            .next()
            .map(|s| s.parse::<UserId>())
            .wrap_err("no user in combo")?
            .wrap_err("failed to parse user in combo")?;
        let promo = split
            .next()
            .map(|s| s.parse::<PromocodeId>())
            .wrap_err("no promo in combo")?
            .wrap_err("failed to parse promo in combo")?;

        try_referral(ctx, referral).await?;
        try_promo(ctx, promo).await?;
    } else if let Some(referral) = text.strip_prefix("ref-") {
        tracing::debug!("on_start_referral: {referral}");

        let referral = referral.parse::<UserId>()?;

        try_referral(ctx, referral).await?;
    } else if let Some(promo) = text.strip_prefix("promo-") {
        tracing::debug!("on_start_promo: {promo}");

        let promo = promo.parse::<PromocodeId>()?;

        try_promo(ctx, promo).await?;
    }

    Ok(())
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username, promo = %promocode))]
async fn try_promo(ctx: &mut Context, promocode: PromocodeId) -> HandlerResult {
    let res = ctx
        .db
        .activate_promocode_by_id(ctx.user.id, promocode)
        .await?;

    ctx.delete_bot_message().await?;

    match res {
        db::PromocodeActivation::Activated(promo) => {
            match promo.kind {
                crate::common::PromocodeKind::Sale => {
                    ctx.user.active_promocode_id = Some(promo.id.clone());
                    ctx.bot
                        .send_message(
                            ChatId(ctx.user.id.0),
                            format!(
                                "✅ Промокод '{}' (-{}%) был успешно активирован",
                                promo.text, promo.value
                            ),
                        )
                        .await?;
                }
                crate::common::PromocodeKind::Balance => {
                    ctx.user.balance += promo.value as f64;
                    ctx.bot
                        .send_message(
                            ChatId(ctx.user.id.0),
                            format!(
                                "✅ Промокод '{}' был успешно активирован! На ваш баланс добавлено ${} USD",
                                promo.text, promo.value
                            ),
                        )
                        .await?;
                }
            }
            ctx.notify_promocode_activated(promo);
        }
        db::PromocodeActivation::NotFound => {
            ctx.bot
                .send_message(ChatId(ctx.user.id.0), "Этот промокод не активен")
                .await?;
        }
        db::PromocodeActivation::ActivatedInThePast => {
            ctx.bot
                .send_message(
                    ChatId(ctx.user.id.0),
                    "Этот промокод уже был активирован вами ранее",
                )
                .await?;
        }
        db::PromocodeActivation::TimeBound => {
            ctx.bot
                .send_message(
                    ChatId(ctx.user.id.0),
                    "Этот промокод не активен в текущий момент",
                )
                .await?;
        }
        db::PromocodeActivation::AlreadyActive(promo) => {
            ctx.bot
                .send_message(
                    ChatId(ctx.user.id.0),
                    format!("Промокод '{}' уже активен", promo.text),
                )
                .await?;
        }
    }

    Ok(())
}

#[instrument(skip_all, fields(user_id = %ctx.user.id, bot = %ctx.bot_username))]
async fn try_referral(ctx: &mut Context, referral_id: UserId) -> HandlerResult {
    if !ctx.user.first_time {
        tracing::debug!("User was already referred");
        return Ok(());
    }

    tracing::debug!("User was referred by {referral_id}");
    ctx.user.referred_by = Some(referral_id);
    ctx.save_user().await?;
    ctx.notify_user_referred(referral_id).await;

    Ok(())
}
