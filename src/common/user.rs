use std::collections::{HashSet, VecDeque};

use serde::Deserialize;

use crate::{
    common::{InvoiceId, ProductId, PromocodeId, Step, UserId},
    db::<PERSON><PERSON>,
};

#[derive(Clone, Debug, Deserialize)]
pub struct User {
    pub id: UserId,
    pub username: Option<String>,
    pub cart: Json<HashSet<ProductId>>,
    pub path: Json<VecDeque<Step>>,
    pub balance: f64,
    pub pending_invoice_id: Option<InvoiceId>,
    pub is_admin: bool,
    pub active_promocode_id: Option<PromocodeId>,
    pub referred_by: Option<UserId>,

    #[serde(skip, default)]
    pub first_time: bool,
}
