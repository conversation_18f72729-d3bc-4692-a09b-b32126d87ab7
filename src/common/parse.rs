pub trait NomParse: Sized {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>;
}

#[macro_export]
macro_rules! string_enum {
    (pub enum $name:ident {
        $($step:ident $( ( $($id:ty),+ ) )? ),*
        $(,)?
    } $(, $der:ty)*) => {
        paste::paste!{
            #[derive(Clone, Debug, PartialEq, Eq, Hash $(,$der),*)]
            pub enum $name {
                $($step $( ( $($id),+ ) )? ,)*
            }

            impl $crate::common::NomParse for $name {
                #[allow(unused_parens, unused_imports)]
                fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>{
                    use nom::{
                        branch::alt,
                        bytes::complete::tag,
                        character::complete::{alpha1, char},
                        combinator::{map, opt, map_res},
                        sequence::{delimited, terminated},
                        Parser, Input, AsChar
                    };

                    $(
                        let [<$step:snake _parser>] =  map(
                            (
                                tag(stringify!([<$step:snake>]))
                                $(,delimited(
                                    char('('),
                                    (
                                        $(
                                            terminated(
                                                <$id as $crate::common::NomParse>::parser(),
                                                opt(char(','))
                                            )
                                        ),+
                                    ),
                                    char(')'),
                                ))?
                            ),
                            |(_ $(, ( $( [<$id:snake>]),* ) )? )| {
                                $name::$step $((
                                    $([<$id:snake>]),+
                                ))?
                            }
                        );
                    )+

                    nom::combinator::fail()
                    $(
                        .or([<$step:snake _parser>])
                    )+

                    // alt((
                    // ))
                }
            }

            impl std::str::FromStr for $name {
                type Err = color_eyre::Report;

                #[allow(unused_imports)]
                fn from_str(s: &str) -> Result<Self, Self::Err> {
                    use nom::Parser;
                    let (_, result) = nom::combinator::all_consuming(<$name as $crate::common::NomParse>::parser()).parse(s)
                        .map_err(|e| color_eyre::eyre::eyre!("Failed to parse {}: {e}", stringify!($name)))?;
                    Ok(result)
                }
            }

            impl ::std::fmt::Display for $name {
                fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
                    match self {
                        $(Self::$step $(( $([<$id:snake>]),+ ))? =>
                            {
                                write!(f, stringify!([<$step:snake>]))?;
                                $(
                                    write!(f, "(")?;
                                    write!(f, "{}", [ $( [<$id:snake>].to_string() ),+ ].join(","))?;
                                    write!(f, ")")?;
                                )?
                                Ok(())
                            }
                        )*
                    }
                }
            }

            impl<'de> ::serde::Deserialize<'de> for $name {
                fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
                where
                    D: ::serde::Deserializer<'de>,
                {
                    let s = String::deserialize(deserializer)?;
                    s.parse().map_err(serde::de::Error::custom)
                }
            }

            impl ::serde::Serialize for $name {
                fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
                where
                    S: ::serde::Serializer,
                {
                    serializer.serialize_str(&self.to_string())
                }
            }
        }
    };
}

impl NomParse for u32 {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::character::complete::u32
    }
}

impl NomParse for i32 {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::character::complete::i32
    }
}

impl NomParse for u8 {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::character::complete::u8
    }
}

impl NomParse for u16 {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::character::complete::u16
    }
}

impl NomParse for u64 {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::character::complete::u64
    }
}

impl NomParse for i8 {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::character::complete::i8
    }
}

impl NomParse for i16 {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::character::complete::i16
    }
}

impl NomParse for i64 {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::character::complete::i64
    }
}

impl NomParse for String {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::combinator::map(nom::bytes::complete::take_while(|c| c != ')'), |x: &str| {
            x.to_string()
        })
    }
}

impl NomParse for uuid::Uuid {
    fn parser<'a>() -> impl nom::Parser<&'a str, Output = Self, Error = nom::error::Error<&'a str>>
    {
        nom::combinator::map(nom::bytes::complete::take_while(|c| c != ')'), |x: &str| {
            uuid::Uuid::parse_str(x).unwrap()
        })
    }
}
