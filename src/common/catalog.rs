use crate::db::<PERSON><PERSON>;

crate::id!(Directory(String));

impl Default for Directory {
    fn default() -> Self {
        Self("root".to_owned())
    }
}

#[derive(serde::Deserialize, Debug)]
#[serde(untagged)]
pub enum CatalogNavKind {
    Directory { dir: Directory },
    ProductCategory { product_category: String },
    Url { url: String },
}

#[derive(serde::Deserialize, Debug)]
pub struct CatalogNav {
    pub btn: String,
    #[serde(flatten)]
    pub kind: CatalogNavKind,
}

#[derive(serde::Deserialize, Debug)]
pub struct CatalogEntry {
    pub id: Directory,
    pub media: String,
    pub description: Option<String>,
    pub nav: Json<Option<Vec<CatalogNav>>>,
}
