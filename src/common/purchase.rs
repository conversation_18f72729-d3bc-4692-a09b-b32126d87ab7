use serde::Deserialize;

use crate::common::{ProductId, PromocodeId, UserId};

#[derive(Debug, Clone, Deserialize)]
pub struct Purchase {
    pub id: i64,
    pub user_id: UserId,
    pub product_id: ProductId,
    pub promo_id: Option<PromocodeId>,
    pub amount: u32,
    pub paid: f64,
    pub date: String,
    pub referred_by: Option<UserId>,
    pub referral_percent: Option<f64>,
    pub production_cost: f64,
}
