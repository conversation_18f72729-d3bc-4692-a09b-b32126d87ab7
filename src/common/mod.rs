mod catalog;
mod notifications;
mod parse;
mod product;
mod promo;
mod purchase;
mod user;

use crate::tiposhop::{admin::AdminStep, catalog::CatalogStep};

pub use {
    catalog::*, notifications::*, parse::NomParse, product::*, promo::*, purchase::*, user::*,
};

crate::id!(InvoiceId(i64), Copy);
crate::id!(UserId(i64), Copy);

crate::string_enum!(
    pub enum Step {
        Default,
        Menu,
        Catalog(CatalogStep),
        Profile,
        Cart,
        Checkout,
        AddBalance,
        Admin(AdminStep),
        Referral,
    }
);

impl Default for Step {
    fn default() -> Self {
        Self::Default
    }
}

crate::string_enum!(
    pub enum Callback {
        Empty,
        Step(Step),
        CancelPay(InvoiceId),
        Payed(InvoiceId),
        Back,
        Minus(ProductId),
        Plus(ProductId),
        Restart,
    }
);
