use std::fmt::Display;

use serde::Deserialize;

crate::id!(PromocodeId(String));

crate::db_enum!(
    pub enum PromocodeKind {
        Sale,
        Balance,
    }
);

#[derive(Clone, Debug, Deserialize)]
pub struct Promocode {
    pub id: PromocodeId,
    pub text: String,

    pub value: i32, // discount for sale, balance for balance
    pub kind: PromocodeKind,
    pub hidden: bool,

    // pub start_date: String, //datetime
    // pub end_date: String,   //datetime
    /// how many times this promo has been activated
    pub activations: i32,
    pub activations_max: Option<i32>,
    // / how many times this promo has been used in a purchase
    // pub usages: i32,
    // pub usages_max: Option<i32>,
}

impl Display for Promocode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{} ({})", self.text, self.id)
    }
}
