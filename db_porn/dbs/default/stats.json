{"id": "5ccf4570-3646-4ea3-96b9-1281448e5993", "rows_written": 120, "rows_read": 412, "storage_bytes_used": 86016, "write_requests_delegated": 0, "current_frame_no": 54, "top_query_threshold": 1, "top_queries": [{"rows_written": 0, "rows_read": 1, "query": "SELECT hash FROM migrations WHERE name = ?;"}, {"rows_written": 0, "rows_read": 1, "query": "SELECT media_file_id FROM uploaded_medias WHERE bot_username = ? AND media_name = ?;"}, {"rows_written": 0, "rows_read": 1, "query": "SELECT value FROM attributes WHERE KEY = ?;"}, {"rows_written": 0, "rows_read": 2, "query": "SELECT * FROM purchases WHERE date > '2025-04-17' AND date >= ? AND date < ? ORDER BY date DESC;"}, {"rows_written": 1, "rows_read": 1, "query": "INSERT INTO attributes (KEY, value) VALUES (?, ?) ON CONFLICT (KEY) DO UPDATE SET value = excluded.value;"}, {"rows_written": 1, "rows_read": 1, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;"}, {"rows_written": 1, "rows_read": 1, "query": "UPDATE catalog SET media = ? WHERE \"id\" = ?;"}, {"rows_written": 1, "rows_read": 1, "query": "UPDATE users SET is_admin = ? WHERE \"id\" = ?;"}, {"rows_written": 2, "rows_read": 0, "query": "REPLACE INTO uploaded_medias (bot_username, media_name, media_file_id) VALUES (?, ?, ?);"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;"}], "slowest_query_threshold": 3, "slowest_queries": [{"elapsed_ms": 3, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 3, "query": "SELECT * FROM purchases WHERE date > '2025-04-17' AND date >= ? AND date < ? ORDER BY date DESC;", "rows_written": 0, "rows_read": 2}, {"elapsed_ms": 3, "query": "UPDATE catalog SET media = ? WHERE \"id\" = ?;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 4, "query": "COMMIT;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 4, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 4, "query": "SELECT media_file_id FROM uploaded_medias WHERE bot_username = ? AND media_name = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 6, "query": "SELECT * FROM users WHERE id = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 6, "query": "SELECT media_file_id FROM uploaded_medias WHERE bot_username = ? AND media_name = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 6, "query": "SELECT value FROM attributes WHERE KEY = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 7, "query": "CREATE TABLE IF NOT EXISTS migrations(name TEXT PRIMARY KEY, hash TEXT)", "rows_written": 3, "rows_read": 1}], "embedded_replica_frames_replicated": 0, "query_count": 221, "query_latency": 188440}