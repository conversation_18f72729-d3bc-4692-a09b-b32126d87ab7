FROM rust:1 AS chef 
# We only pay the installation cost once, 
# it will be cached from the second build onwards
RUN cargo install cargo-chef 
RUN apt-get update && apt-get install -y pkg-config libssl-dev cmake && rm -rf /var/lib/apt/lists/*
WORKDIR /app

FROM chef AS planner
COPY . .
RUN cargo chef prepare 

FROM chef AS builder
COPY --from=planner /app/recipe.json .
COPY --from=planner /app/grammers /app/grammers
RUN cargo chef cook --release
COPY . .
RUN cargo build --release
RUN mv ./target/release/ded-tg-shop ./app

FROM debian:bookworm-slim AS runtime
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
WORKDIR /app
COPY --from=builder /app/app /app/exe
COPY --from=builder /app/assets /app/assets
ENTRYPOINT ["/app/exe"]