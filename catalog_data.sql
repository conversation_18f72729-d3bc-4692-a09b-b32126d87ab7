-- SQL script to populate catalog table with directories and product categories
-- Based on the JSON structure from src/tiposhop/catalog.rs

-- Insert directories (entries with navigation)
INSERT INTO catalog (id, kind, media, description, nav) VALUES 
(
    'root',
    'directory',
    'accounts',
    NULL,
    '[
        {
            "btn": "🗂 Канал-каталог анкет",
            "url": "https://example.com"
        },
        {
            "btn": "🔐 Пак кружков (обезличенные)",
            "product_category": "pack"
        },
        {
            "btn": "🧚🏻‍♀️ Модели классика",
            "dir": "classic"
        },
        {
            "btn": "🔞 Модели onlyfans",
            "dir": "onlyfans"
        },
        {
            "btn": "🧖🏻‍♀️ Модели домашние",
            "dir": "home"
        }
    ]'
),
(
    'classic',
    'directory',
    'accounts',
    NULL,
    '[
        {
            "btn": "малый пак",
            "product_category": "classic-small"
        },
        {
            "btn": "средний пак",
            "product_category": "classic-medium"
        },
        {
            "btn": "большой пак",
            "product_category": "classic-big"
        }
    ]'
),
(
    'onlyfans',
    'directory',
    'accounts',
    NULL,
    '[
        {
            "btn": "малый пак",
            "product_category": "onlyfans-small"
        },
        {
            "btn": "средний пак",
            "product_category": "onlyfans-medium"
        },
        {
            "btn": "большой пак",
            "product_category": "onlyfans-big"
        }
    ]'
),
(
    'home',
    'directory',
    'accounts',
    NULL,
    '[
        {
            "btn": "малый пак",
            "product_category": "home-small"
        },
        {
            "btn": "средний пак",
            "product_category": "home-medium"
        },
        {
            "btn": "большой пак",
            "product_category": "home-big"
        }
    ]'
);

-- Insert product categories (entries without navigation)
INSERT INTO catalog (id, kind, media, description, nav) VALUES 
('pack', 'category', 'accounts', NULL, NULL),
('classic-small', 'category', 'accounts', NULL, NULL),
('classic-medium', 'category', 'accounts', NULL, NULL),
('classic-big', 'category', 'accounts', NULL, NULL),
('onlyfans-small', 'category', 'accounts', NULL, NULL),
('onlyfans-medium', 'category', 'accounts', NULL, NULL),
('onlyfans-big', 'category', 'accounts', NULL, NULL),
('home-small', 'category', 'accounts', NULL, NULL),
('home-medium', 'category', 'accounts', NULL, NULL),
('home-big', 'category', 'accounts', NULL, NULL);
